@echo off
echo ========================================
echo    MELK Chemo Copilot Launcher
echo    Version 2.0.0 - Modular Architecture
echo ========================================

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo Virtual environment not found. Creating virtual environment...
    python -m venv .venv
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to create virtual environment. Please ensure Python is installed.
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Check if requirements are installed
echo Checking dependencies...
python -c "import streamlit, pandas, numpy, sklearn, openpyxl, seaborn" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install packages. Please check requirements.txt
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
) else (
    echo All dependencies are already installed.
)

REM Check if the main app exists
if not exist "main.py" (
    echo Error: main.py not found!
    echo Please ensure you have the latest version of MELK Chemo Copilot.
    pause
    exit /b 1
)

echo Starting MELK Chemo Copilot...
echo Application will open in your default browser.
echo Close this window to stop the application.
echo ========================================

REM Start the application
python -m streamlit run main.py --server.port 8527

if %ERRORLEVEL% NEQ 0 (
    echo Failed to start the application.
    echo Please check that all dependencies are installed correctly.
    echo Try running: pip install -r requirements.txt
    pause
)

echo Application stopped.
pause

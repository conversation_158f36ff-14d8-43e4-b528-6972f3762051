"""
Test Enhanced ANN integration in MELK Chemo Copilot Step 5
"""

import sys
import os
sys.path.insert(0, os.getcwd())

def test_enhanced_ann_integration():
    """Test the Enhanced ANN integration in Step 5."""
    try:
        from utils.enhanced_ann import EnhancedANN
        from sklearn.datasets import make_regression
        import numpy as np
        
        print("🧪 Testing Enhanced ANN Integration in MELK Chemo Copilot")
        print("=" * 70)
        
        # Generate test data
        X, y = make_regression(n_samples=100, n_features=50, noise=0.1, random_state=42)
        print(f"📊 Test Data: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Test 1: Basic Enhanced ANN functionality
        print("\n🎯 Test 1: Basic Enhanced ANN")
        print("-" * 35)
        
        model = EnhancedANN(
            hidden_layer_sizes=(10,),
            activation='tanh',
            solver='lbfgs',
            use_pca=True,
            pca_variance_threshold=0.95,
            data_division='sequential',
            train_ratio=0.48,
            val_ratio=0.16,
            normalize_data=True,
            max_iter=500,
            random_state=42
        )
        
        model.fit(X, y)
        y_pred = model.predict(X)
        r2 = model.score(X, y)
        info = model.get_model_info()
        
        print(f"✅ Model trained successfully")
        print(f"   Architecture: {info['architecture']}")
        print(f"   Activation: {info['activation']}")
        print(f"   Solver: {info['solver']}")
        print(f"   PCA Components: {info.get('pca_components', 'N/A')}")
        print(f"   PCA Variance: {info.get('pca_variance_explained', 'N/A'):.4f}")
        print(f"   Data Division: {info['data_division']}")
        print(f"   R² Score: {r2:.4f}")
        
        # Print training history
        print(f"\n📊 Training History:")
        for split, metrics in info['training_history'].items():
            print(f"   {split.capitalize()}: R²={metrics['r2']:.4f}, RMSE={metrics['rmse']:.4f}, n={metrics['n_samples']}")
        
        # Test 2: MATLAB-style configuration
        print("\n🎯 Test 2: MATLAB-style Configuration")
        print("-" * 40)
        
        matlab_model = EnhancedANN(
            hidden_layer_sizes=(5,),      # Like MATLAB newff with 5 neurons
            activation='tanh',            # Like MATLAB tansig
            solver='lbfgs',              # Like MATLAB trainlm
            use_pca=True,
            data_division='sequential',   # Like MATLAB divideblock
            train_ratio=0.48,            # Like MATLAB net.divideParam.trainRatio
            val_ratio=0.16,              # Like MATLAB net.divideParam.valRatio
            normalize_data=True,         # Like MATLAB mapstd
            max_iter=500,
            random_state=42
        )
        
        matlab_model.fit(X, y)
        matlab_r2 = matlab_model.score(X, y)
        matlab_info = matlab_model.get_model_info()
        
        print(f"✅ MATLAB-style model trained successfully")
        print(f"   R² Score: {matlab_r2:.4f}")
        print(f"   Iterations: {matlab_info.get('n_iterations', 'N/A')}")
        print(f"   PCA Components: {matlab_info.get('pca_components', 'N/A')}")
        
        # Test 3: Compare with standard MLP
        print("\n🎯 Test 3: Comparison with Standard MLP")
        print("-" * 42)
        
        from sklearn.neural_network import MLPRegressor
        from sklearn.model_selection import train_test_split
        
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        # Standard MLP
        mlp = MLPRegressor(
            hidden_layer_sizes=(10,),
            activation='tanh',
            solver='lbfgs',
            max_iter=500,
            random_state=42
        )
        mlp.fit(X_train, y_train)
        mlp_r2 = mlp.score(X_test, y_test)
        
        # Enhanced ANN
        enhanced = EnhancedANN(
            hidden_layer_sizes=(10,),
            activation='tanh',
            solver='lbfgs',
            use_pca=False,  # Disable PCA for fair comparison
            normalize_data=True,
            max_iter=500,
            random_state=42
        )
        enhanced.fit(X_train, y_train)
        enhanced_r2 = enhanced.score(X_test, y_test)
        
        print(f"Standard MLP R²: {mlp_r2:.4f}")
        print(f"Enhanced ANN R²: {enhanced_r2:.4f}")
        print(f"Difference: {abs(enhanced_r2 - mlp_r2):.4f}")
        
        if abs(enhanced_r2 - mlp_r2) < 0.1:
            print("✅ Performance is comparable (good!)")
        else:
            print("⚠️ Performance differs significantly")
        
        # Test 4: PCA Integration
        print("\n🎯 Test 4: PCA Integration Test")
        print("-" * 35)
        
        # Without PCA
        no_pca_model = EnhancedANN(
            hidden_layer_sizes=(10,),
            use_pca=False,
            normalize_data=True,
            random_state=42
        )
        no_pca_model.fit(X, y)
        no_pca_r2 = no_pca_model.score(X, y)
        
        # With PCA
        pca_model = EnhancedANN(
            hidden_layer_sizes=(10,),
            use_pca=True,
            pca_variance_threshold=0.95,
            normalize_data=True,
            random_state=42
        )
        pca_model.fit(X, y)
        pca_r2 = pca_model.score(X, y)
        pca_info = pca_model.get_model_info()
        
        print(f"Without PCA R²: {no_pca_r2:.4f}")
        print(f"With PCA R²: {pca_r2:.4f}")
        print(f"PCA Components: {pca_info.get('pca_components', 'N/A')}")
        print(f"Original Features: {X.shape[1]}")
        print(f"Dimensionality Reduction: {X.shape[1] - pca_info.get('pca_components', X.shape[1])}")
        
        # Test 5: Data Division Methods
        print("\n🎯 Test 5: Data Division Methods")
        print("-" * 37)
        
        division_methods = ['sequential', 'random', 'interleaved']
        
        for method in division_methods:
            div_model = EnhancedANN(
                hidden_layer_sizes=(10,),
                data_division=method,
                train_ratio=0.6,
                val_ratio=0.2,
                test_ratio=0.2,
                random_state=42
            )
            div_model.fit(X, y)
            div_info = div_model.get_model_info()
            
            print(f"{method.capitalize()} Division:")
            for split, metrics in div_info['training_history'].items():
                print(f"  {split}: n={metrics['n_samples']}, R²={metrics['r2']:.4f}")
        
        print("\n🎉 All Enhanced ANN Integration Tests Passed!")
        print("\n📋 Summary:")
        print("✅ Enhanced ANN is working correctly")
        print("✅ MATLAB-style features are functional")
        print("✅ PCA integration is working")
        print("✅ Data division methods are working")
        print("✅ Normalization is working")
        print("✅ Ready for integration into MELK Chemo Copilot")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced ANN integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_step5_integration():
    """Test if Enhanced ANN can be imported in Step 5."""
    try:
        print("\n🔧 Testing Step 5 Integration")
        print("-" * 35)
        
        # Test import
        from steps.step5_model_selection import ModelSelectionStep
        print("✅ Step 5 import successful")
        
        # Test Enhanced ANN import within Step 5
        from utils.enhanced_ann import EnhancedANN
        print("✅ Enhanced ANN import successful")
        
        print("✅ Step 5 integration ready")
        return True
        
    except Exception as e:
        print(f"❌ Step 5 integration test failed: {e}")
        return False


def main():
    """Run all integration tests."""
    print("🚀 Enhanced ANN Integration Test Suite")
    print("=" * 50)
    
    # Test Enhanced ANN functionality
    ann_success = test_enhanced_ann_integration()
    
    # Test Step 5 integration
    step5_success = test_step5_integration()
    
    if ann_success and step5_success:
        print("\n🎉 All Integration Tests Passed!")
        print("🚀 Enhanced ANN is ready for use in MELK Chemo Copilot!")
    else:
        print("\n💥 Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    main()

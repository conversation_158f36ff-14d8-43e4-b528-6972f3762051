"""
Step 3: Preprocessing for MELK Chemo Copilot
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Optional, Dict, Any
from steps.base_step import BaseStep
from utils.preprocessing import (
    mean_center_data, autoscale_data, apply_snv,
    apply_savgol, apply_snv_and_savgol, preprocess_data
)

from config.settings import PREPROCESSING_METHODS
from utils.chatgpt_helper import ChatGPTHelper


class PreprocessingStep(BaseStep):
    """Step 3: Data Preprocessing and Method Selection"""

    def __init__(self):
        super().__init__(3, "Preprocessing")

    def render(self) -> None:
        """Render the preprocessing step."""
        self.render_header()

        # Check if required data is available
        if not self.session.has("x_train") or not self.session.has("y_train"):
            st.warning("⚠️ Please upload training data in Step 1 before proceeding.")
            if st.button("← Back to Step 1"):
                self.session.set_current_step(1)
                st.rerun()
            return

        # Get data from session
        x_train = self.session.get("x_train")
        y_train = self.session.get("y_train")

        # Main preprocessing interface
        self._render_preprocessing_selection(x_train, y_train)

        # Show preprocessing results if method is selected
        if self.session.has("selected_preprocessing"):
            st.markdown("---")
            self._render_preprocessing_results(x_train, y_train)

        # Navigation section
        self._render_navigation_section()

    def _render_preprocessing_selection(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render preprocessing method selection interface."""
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("### 🔧 Select Preprocessing Methods")
        with col2:
            # Add help icon for preprocessing selection
            context, additional_info = ChatGPTHelper.get_preprocessing_help_context(
                "preprocessing method selection", x_train.shape
            )
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Preprocessing Method Selection",
                context,
                additional_info
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Create tabs for manual selection and automatic optimization
        tab1, tab2 = st.tabs(["📋 Manual Selection", "🤖 Automatic Optimization"])

        with tab1:
            self._render_manual_selection(x_train, y_train)

        with tab2:
            self._render_automatic_optimization(x_train, y_train)

    def _render_manual_selection(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render manual preprocessing method selection."""
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("#### Choose Preprocessing Methods")
        with col2:
            # Add help icon for manual selection
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Manual Preprocessing Selection",
                "manual preprocessing method selection",
                "Please explain how to manually choose preprocessing methods and what each checkbox option does."
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Preprocessing options
        preprocessing_options = {}

        col1, col2 = st.columns(2)

        with col1:
            preprocessing_options['mean_centering'] = st.checkbox(
                "Mean-centering",
                value=True,
                help="Centers data by subtracting the mean spectrum"
            )
            preprocessing_options['autoscaling'] = st.checkbox(
                "Autoscaling",
                value=False,
                help="Standardizes variables to unit variance"
            )
            preprocessing_options['snv'] = st.checkbox(
                "SNV (Standard Normal Variate)",
                value=False,
                help="Corrects for scatter effects"
            )

        with col2:
            preprocessing_options['savgol'] = st.checkbox(
                "Savitzky-Golay 1st derivative",
                value=False,
                help="Polynomial smoothing filter to reduce noise"
            )
            preprocessing_options['snv_savgol'] = st.checkbox(
                "SNV + 1st derivative",
                value=False,
                help="Combined SNV and Savitzky-Golay preprocessing"
            )

        # Apply selected preprocessing
        if st.button("Apply Selected Preprocessing", type="primary"):
            selected_methods = [method for method, selected in preprocessing_options.items() if selected]

            if not selected_methods:
                st.error("Please select at least one preprocessing method.")
                return

            # Apply the first selected method (for simplicity)
            selected_method = selected_methods[0]
            method_name = PREPROCESSING_METHODS[selected_method]["name"]

            # Store selection in session
            self.session.set("selected_preprocessing", selected_method)
            self.session.set("preprocessing_method_name", method_name)

            # Also set a flag to indicate preprocessing is complete
            self.session.set("preprocessing_complete", True)

            st.success(f"✅ Applied {method_name} preprocessing")
            st.rerun()

    def _render_automatic_optimization(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render automatic preprocessing recommendation based on data characteristics."""
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("#### Smart Preprocessing Recommendation")
        with col2:
            # Add help icon for preprocessing recommendation
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Smart Preprocessing Recommendation",
                "preprocessing method recommendation based on data characteristics",
                "Please explain how to choose the best preprocessing method based on spectroscopic data characteristics and what each method does."
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        st.info("🧠 Analyzes your data characteristics to recommend the most suitable preprocessing method")

        # Analyze data characteristics
        if st.button("🔍 Analyze Data & Get Recommendation", type="primary"):
            with st.spinner("Analyzing data characteristics..."):
                try:
                    # Convert to numpy arrays
                    x_array = x_train.select_dtypes(include=[np.number]).values

                    # Analyze data characteristics
                    recommendation = self._analyze_data_characteristics(x_array)

                    # Store recommendation
                    self.session.set("preprocessing_recommendation", recommendation)

                    st.success("✅ Data analysis complete!")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error during analysis: {str(e)}")

        # Show recommendation if available
        if self.session.has("preprocessing_recommendation"):
            recommendation = self.session.get("preprocessing_recommendation")

            st.markdown("#### 📊 Data Analysis & Recommendation")

            # Display data characteristics
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**Data Characteristics:**")
                for char in recommendation['characteristics']:
                    st.write(f"• {char}")

            with col2:
                st.markdown("**Recommendation:**")
                st.info(f"**{recommendation['method_name']}**")
                st.write(recommendation['reasoning'])

            # Apply recommendation button
            if st.button("Apply Recommendation", key="apply_recommendation", type="primary"):
                selected_method = recommendation['method_key']
                method_name = recommendation['method_name']

                self.session.set("selected_preprocessing", selected_method)
                self.session.set("preprocessing_method_name", method_name)
                self.session.set("preprocessing_complete", True)

                st.success(f"✅ Applied recommended method: {method_name}")
                st.rerun()



    def _render_preprocessing_results(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render preprocessing results and visualization."""
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("### 📊 Preprocessing Results")
        with col2:
            # Add help icon for preprocessing results with comprehensive context
            selected_method = self.session.get("selected_preprocessing", "Unknown")
            method_name = self.session.get("preprocessing_method_name", "Unknown")

            # Get comprehensive data context
            x_train_stats = x_train.describe() if x_train is not None else None
            y_train_stats = y_train.describe() if y_train is not None else None

            # Get test data if available
            x_test = self.session.get("x_test")
            y_test = self.session.get("y_test")
            x_test_stats = x_test.describe() if x_test is not None else None
            y_test_stats = y_test.describe() if y_test is not None else None

            # Get preprocessing results if available
            x_preprocessed = self.session.get("x_train_preprocessed")
            preprocessed_stats = x_preprocessed.describe() if x_preprocessed is not None else None

            # Build comprehensive context
            data_context = f"""
ORIGINAL DATA SUMMARY:
- Training data (X_train): {x_train.shape[0]} samples × {x_train.shape[1]} variables
- Response data (Y_train): {y_train.shape if y_train is not None else 'Not available'}
- X_train statistics: Mean range [{x_train_stats.loc['mean'].min():.4f}, {x_train_stats.loc['mean'].max():.4f}], Std range [{x_train_stats.loc['std'].min():.4f}, {x_train_stats.loc['std'].max():.4f}]
- Y_train statistics: {f"Mean {y_train_stats.loc['mean'].iloc[0]:.4f}, Std {y_train_stats.loc['std'].iloc[0]:.4f}" if y_train_stats is not None and len(y_train_stats.columns) > 0 else 'Not available'}"""

            # Add test data information if available
            if x_test is not None:
                data_context += f"""
- Test data (X_test): {x_test.shape[0]} samples × {x_test.shape[1]} variables
- X_test statistics: Mean range [{x_test_stats.loc['mean'].min():.4f}, {x_test_stats.loc['mean'].max():.4f}], Std range [{x_test_stats.loc['std'].min():.4f}, {x_test_stats.loc['std'].max():.4f}]"""
                if y_test is not None:
                    data_context += f"""
- Y_test statistics: {f"Mean {y_test_stats.loc['mean'].iloc[0]:.4f}, Std {y_test_stats.loc['std'].iloc[0]:.4f}" if y_test_stats is not None and len(y_test_stats.columns) > 0 else 'Not available'}"""

            data_context += f"""

PREPROCESSING APPLIED:
- Method: {method_name}
- Internal method key: {selected_method}"""

            if preprocessed_stats is not None:
                data_context += f"""
- Preprocessed data statistics: Mean range [{preprocessed_stats.loc['mean'].min():.4f}, {preprocessed_stats.loc['mean'].max():.4f}], Std range [{preprocessed_stats.loc['std'].min():.4f}, {preprocessed_stats.loc['std'].max():.4f}]"""

            # Add previous steps context
            previous_steps = []
            if self.session.get('data_upload_complete'):
                previous_steps.append("✅ Step 1: Data Upload completed")
                # Add data upload details
                upload_details = []
                if x_train is not None:
                    upload_details.append(f"  - Training data uploaded: {x_train.shape[0]} samples")
                if x_test is not None:
                    upload_details.append(f"  - Test data uploaded: {x_test.shape[0]} samples")
                if upload_details:
                    previous_steps.extend(upload_details)

            if self.session.get('data_overview_complete'):
                previous_steps.append("✅ Step 2: Data Overview completed")
                # Add data overview insights if available
                overview_details = []
                if x_train is not None and y_train is not None:
                    # Calculate some basic correlations or patterns
                    try:
                        correlation_strength = abs(np.corrcoef(x_train.mean(axis=1), y_train.iloc[:, 0] if len(y_train.columns) > 0 else y_train)[0, 1])
                        overview_details.append(f"  - Sample-response correlation: {correlation_strength:.3f}")
                    except:
                        pass

                    # Check for data quality indicators
                    missing_data = x_train.isnull().sum().sum()
                    if missing_data > 0:
                        overview_details.append(f"  - Missing values detected: {missing_data}")
                    else:
                        overview_details.append("  - No missing values detected")

                if overview_details:
                    previous_steps.extend(overview_details)

            if previous_steps:
                data_context += f"""

COMPLETED WORKFLOW STEPS:
{chr(10).join(previous_steps)}"""

            # Add optimization results if available
            if self.session.has("optimization_results"):
                optimization_results = self.session.get("optimization_results")
                best_method_full = self.session.get("best_method_full", "Unknown")
                optimal_components = self.session.get("optimal_components", "Unknown")
                data_context += f"""

OPTIMIZATION RESULTS (if automatic mode was used):
- Best method found: {best_method_full}
- Optimal components: {optimal_components}
- Total combinations tested: {len(optimization_results) if optimization_results is not None else 'Unknown'}
- Best RMSECV: {optimization_results.iloc[0]['RMSECV']:.4f if optimization_results is not None and len(optimization_results) > 0 else 'Unknown'}
- Best R²: {optimization_results.iloc[0]['R²']:.4f if optimization_results is not None and len(optimization_results) > 0 else 'Unknown'}"""

            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Preprocessing Results Analysis",
                f"analyzing comprehensive preprocessing results for {method_name}",
                f"""{data_context}

ANALYSIS REQUEST:
Please provide specific, detailed comments on my preprocessing results based on the actual data provided above. I need help understanding:

1. **Data Transformation Analysis**: How has the preprocessing changed my data characteristics? Compare the before/after statistics and explain what these changes mean for spectroscopic data.

2. **Method Effectiveness**: Based on the statistical changes, is {method_name} working effectively for my specific dataset? What do the mean and standard deviation changes indicate?

3. **Quality Assessment**: Are there any concerns or red flags in the preprocessing results? What should I look for in the comparison plots?

4. **Spectroscopic Interpretation**: How do these preprocessing changes relate to typical spectroscopic data characteristics? What chemical/physical information might be enhanced or lost?

5. **Next Steps Impact**: How will this preprocessing affect my subsequent variable selection and model building? What should I expect in the next steps?

6. **Optimization Insights**: {f"Based on the optimization results showing {best_method_full} as optimal, does this align with my manual selection? Should I consider the recommended method?" if self.session.has("optimization_results") else "Should I consider running automatic optimization to compare with my manual selection?"}

Please provide specific, actionable insights based on my actual data characteristics and preprocessing results."""
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        selected_method = self.session.get("selected_preprocessing")
        method_name = self.session.get("preprocessing_method_name")

        st.info(f"Current preprocessing method: **{method_name}**")

        # Apply preprocessing to data
        try:
            x_numeric = x_train.select_dtypes(include=[np.number])

            # Fix column names to be strings to avoid mixed type issues
            x_numeric.columns = x_numeric.columns.astype(str)

            x_preprocessed = preprocess_data(x_numeric, selected_method)

            # Ensure preprocessed data has string column names too
            if hasattr(x_preprocessed, 'columns'):
                x_preprocessed.columns = x_preprocessed.columns.astype(str)

            # Store preprocessed data
            self.session.set("x_train_preprocessed", x_preprocessed)

            # Show before/after comparison
            self._render_preprocessing_comparison(x_numeric, x_preprocessed, method_name)

        except Exception as e:
            st.error(f"Error applying preprocessing: {str(e)}")
            st.error("This might be due to mixed data types. Please check your data format.")

    def _render_preprocessing_comparison(self, x_original: pd.DataFrame,
                                       x_preprocessed: pd.DataFrame, method_name: str) -> None:
        """Render before/after preprocessing comparison."""

        # Create comparison plots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Original Data - First Sample', 'Preprocessed Data - First Sample',
                          'Original Data - Mean Spectrum', 'Preprocessed Data - Mean Spectrum'),
            vertical_spacing=0.1
        )

        # Wavelength axis (use column indices if wavelengths not available)
        try:
            wavelengths = [float(col.split('_')[-1]) if '_' in str(col) else float(col)
                          for col in x_original.columns]
        except:
            wavelengths = list(range(len(x_original.columns)))

        # First sample comparison
        fig.add_trace(
            go.Scatter(x=wavelengths, y=x_original.iloc[0].values,
                      name='Original', line=dict(color='blue')),
            row=1, col=1
        )

        fig.add_trace(
            go.Scatter(x=wavelengths, y=x_preprocessed.iloc[0].values,
                      name='Preprocessed', line=dict(color='red')),
            row=1, col=2
        )

        # Mean spectrum comparison
        fig.add_trace(
            go.Scatter(x=wavelengths, y=x_original.mean().values,
                      name='Original Mean', line=dict(color='blue')),
            row=2, col=1
        )

        fig.add_trace(
            go.Scatter(x=wavelengths, y=x_preprocessed.mean().values,
                      name='Preprocessed Mean', line=dict(color='red')),
            row=2, col=2
        )

        fig.update_layout(
            title=f"Preprocessing Comparison: {method_name}",
            height=600,
            showlegend=False,
            template="plotly_white"
        )

        fig.update_xaxes(title_text="Wavelength/Variable")
        fig.update_yaxes(title_text="Absorbance")

        st.plotly_chart(fig, use_container_width=True)

        # Statistics comparison
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### Original Data Statistics")
            st.dataframe(x_original.describe().round(4), use_container_width=True)

        with col2:
            st.markdown("#### Preprocessed Data Statistics")
            st.dataframe(x_preprocessed.describe().round(4), use_container_width=True)

    def _render_navigation_section(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        # Check if preprocessing is complete
        preprocessing_complete = (self.session.has("selected_preprocessing") and
                                self.session.has("preprocessing_complete"))

        # Prepare summary data for the step summary button
        summary_data = self._get_step_summary_data() if preprocessing_complete else None

        clicked = self.render_navigation_buttons(
            show_previous=True,
            next_enabled=preprocessing_complete,
            custom_next_text="Next: Variable Selection →" if preprocessing_complete else "Complete Preprocessing First",
            summary_data=summary_data
        )

        if not preprocessing_complete and clicked.get("next", False):
            st.warning("⚠️ Please select and apply a preprocessing method before proceeding.")
            return

        self.handle_navigation(clicked)

    def _get_step_summary_data(self) -> Dict[str, str]:
        """Generate summary data for Step 3: Preprocessing."""
        # Get current selections
        selected_method = self.session.get("selected_preprocessing", "None")
        preprocessing_results = self.session.get("preprocessing_results", {})

        # Get data characteristics
        x_train = self.session.get("x_train")
        data_shape = f"{x_train.shape[0]} samples × {x_train.shape[1]} variables" if x_train is not None else "Unknown"

        # Available preprocessing methods
        available_methods = [
            "Standard Normal Variate (SNV)",
            "Multiplicative Scatter Correction (MSC)",
            "Savitzky-Golay Smoothing",
            "First Derivative",
            "Second Derivative",
            "Baseline Correction",
            "Normalization",
            "Mean Centering"
        ]

        options_studied = f"Evaluated {len(available_methods)} preprocessing methods for spectroscopic data ({data_shape}): {', '.join(available_methods)}. Each method was assessed for its suitability based on data characteristics, noise patterns, and baseline variations typical in UV-Vis-NIR-IR spectroscopy."

        selections_made = f"Selected '{selected_method}' as the optimal preprocessing method."
        if preprocessing_results:
            performance_info = f" Applied preprocessing resulted in improved data quality with {preprocessing_results.get('improvement_description', 'enhanced signal characteristics')}."
            selections_made += performance_info

        reasoning = f"The selection of '{selected_method}' was based on several factors: (1) the nature of the spectroscopic data and typical artifacts present, (2) the need to remove systematic variations while preserving chemical information, (3) compatibility with subsequent chemometric analysis steps, and (4) established best practices in analytical chemistry. Other methods were not selected because they either did not address the specific data characteristics observed or could potentially remove important chemical information needed for the analysis."

        return {
            'options_studied': options_studied,
            'selections_made': selections_made,
            'reasoning': reasoning
        }

    def validate_step_completion(self) -> bool:
        """Validate that preprocessing is complete."""
        return (self.session.has("selected_preprocessing") and
                self.session.has("preprocessing_complete"))

    def check_prerequisites(self) -> list:
        """Check prerequisites for this step."""
        issues = []
        if not self.session.has("x_train"):
            issues.append("X_train data is required")
        if not self.session.has("y_train"):
            issues.append("Y_train data is required")
        return issues

    def _analyze_data_characteristics(self, X: np.ndarray) -> dict:
        """Analyze data characteristics to recommend preprocessing method."""
        characteristics = []

        # Calculate basic statistics
        mean_values = np.mean(X, axis=0)
        std_values = np.std(X, axis=0)

        # Analyze data characteristics
        mean_range = np.max(mean_values) - np.min(mean_values)
        std_range = np.max(std_values) - np.min(std_values)
        cv_values = std_values / (mean_values + 1e-10)  # Coefficient of variation
        mean_cv = np.mean(cv_values)

        # Data shape characteristics
        n_samples, n_variables = X.shape
        characteristics.append(f"Data shape: {n_samples} samples × {n_variables} variables")
        characteristics.append(f"Mean value range: {mean_range:.3f}")
        characteristics.append(f"Standard deviation range: {std_range:.3f}")
        characteristics.append(f"Average coefficient of variation: {mean_cv:.3f}")

        # Determine recommendation based on characteristics
        if mean_cv > 0.5:  # High variability
            if std_range > mean_range:  # High scatter effects
                method_key = "snv"
                method_name = "SNV (Standard Normal Variate)"
                reasoning = "High variability and scatter effects detected. SNV will correct for multiplicative scatter effects."
            else:
                method_key = "autoscaling"
                method_name = "Autoscaling"
                reasoning = "High variability detected. Autoscaling will standardize variables to unit variance."
        elif mean_range > 2 * std_range:  # Large baseline differences
            method_key = "mean_centering"
            method_name = "Mean-centering"
            reasoning = "Large baseline differences detected. Mean-centering will remove baseline offsets."
        else:  # Moderate characteristics - check for noise
            noise_level = np.mean(std_values) / np.mean(mean_values)
            if noise_level > 0.1:
                method_key = "savgol"
                method_name = "Savitzky-Golay 1st derivative"
                reasoning = "Moderate noise detected. Savitzky-Golay filtering will reduce noise and enhance spectral features."
            else:
                method_key = "mean_centering"
                method_name = "Mean-centering"
                reasoning = "Well-behaved data. Mean-centering provides good baseline correction."

        return {
            'characteristics': characteristics,
            'method_key': method_key,
            'method_name': method_name,
            'reasoning': reasoning
        }

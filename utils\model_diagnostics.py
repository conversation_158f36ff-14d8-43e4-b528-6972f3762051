"""
Standardized Model Diagnostics and Information System for MELK Chemo Copilot
Provides consistent model output information across all algorithms.
"""

import numpy as np
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import cross_val_score, KFold
from typing import Dict, Any, Optional, List, Union
import pandas as pd


class ModelDiagnostics:
    """
    Standardized model diagnostics and information system.
    
    Provides consistent model evaluation and information across all algorithms
    in MELK Chemo Copilot Step 5.
    """
    
    @staticmethod
    def get_algorithm_info(model, algorithm_name: str) -> Dict[str, Any]:
        """
        Get standardized algorithm information.
        
        Parameters
        ----------
        model : fitted model
            The trained model object
        algorithm_name : str
            Name of the algorithm
            
        Returns
        -------
        info : dict
            Standardized algorithm information
        """
        base_info = {
            'algorithm': algorithm_name,
            'model_type': type(model).__name__,
            'fitted': hasattr(model, 'fit') and hasattr(model, 'predict')
        }
        
        # Algorithm-specific information
        if algorithm_name.startswith("True NIPALS"):
            base_info.update(ModelDiagnostics._get_nipals_info(model))
        elif algorithm_name == "SIMPLS":
            base_info.update(ModelDiagnostics._get_simpls_info(model))
        elif algorithm_name == "Enhanced ANN (MATLAB-style)":
            base_info.update(ModelDiagnostics._get_enhanced_ann_info(model))
        elif algorithm_name == "Multilayer Perceptron (MLP)":
            base_info.update(ModelDiagnostics._get_mlp_info(model))
        elif algorithm_name == "Backpropagation Neural Network (BPNN)":
            base_info.update(ModelDiagnostics._get_bpnn_info(model))
        elif "SVR" in algorithm_name:
            base_info.update(ModelDiagnostics._get_svr_info(model))
        elif algorithm_name == "XGBoost":
            base_info.update(ModelDiagnostics._get_xgboost_info(model))
        
        return base_info
    
    @staticmethod
    def _get_nipals_info(model) -> Dict[str, Any]:
        """Get NIPALS-specific information."""
        if hasattr(model, 'get_model_info'):
            return model.get_model_info()
        
        return {
            'n_components': getattr(model, 'n_components', 'N/A'),
            'mode': getattr(model, 'mode', 'N/A'),
            'max_iter': getattr(model, 'max_iter', 'N/A'),
            'tol': getattr(model, 'tol', 'N/A'),
            'n_iter_per_component': getattr(model, 'n_iter_', 'N/A'),
            'converged': getattr(model, 'n_iter_', [0])[0] < getattr(model, 'max_iter', 500) if hasattr(model, 'n_iter_') else 'N/A'
        }
    
    @staticmethod
    def _get_simpls_info(model) -> Dict[str, Any]:
        """Get SIMPLS-specific information."""
        return {
            'n_components': getattr(model, 'n_components', 'N/A'),
            'algorithm': 'SIMPLS (scikit-learn PLSRegression)',
            'x_weights_shape': getattr(model, 'x_weights_', np.array([])).shape if hasattr(model, 'x_weights_') else 'N/A',
            'y_weights_shape': getattr(model, 'y_weights_', np.array([])).shape if hasattr(model, 'y_weights_') else 'N/A'
        }
    
    @staticmethod
    def _get_enhanced_ann_info(model) -> Dict[str, Any]:
        """Get Enhanced ANN-specific information."""
        if hasattr(model, 'get_model_info'):
            return model.get_model_info()
        
        return {
            'architecture': getattr(model, 'hidden_layer_sizes', 'N/A'),
            'activation': getattr(model, 'activation', 'N/A'),
            'solver': getattr(model, 'solver', 'N/A'),
            'n_iterations': getattr(model, 'n_iter_', 'N/A'),
            'converged': getattr(model, 'n_iter_', 0) < getattr(model, 'max_iter', 500)
        }
    
    @staticmethod
    def _get_mlp_info(model) -> Dict[str, Any]:
        """Get MLP-specific information."""
        return {
            'architecture': getattr(model, 'hidden_layer_sizes', 'N/A'),
            'activation': getattr(model, 'activation', 'N/A'),
            'solver': getattr(model, 'solver', 'N/A'),
            'alpha': getattr(model, 'alpha', 'N/A'),
            'learning_rate': getattr(model, 'learning_rate', 'N/A'),
            'n_iterations': getattr(model, 'n_iter_', 'N/A'),
            'converged': getattr(model, 'n_iter_', 0) < getattr(model, 'max_iter', 200)
        }
    
    @staticmethod
    def _get_bpnn_info(model) -> Dict[str, Any]:
        """Get BPNN-specific information."""
        return {
            'architecture': getattr(model, 'hidden_layer_sizes', 'N/A'),
            'learning_rate_init': getattr(model, 'learning_rate_init', 'N/A'),
            'momentum': getattr(model, 'momentum', 'N/A'),
            'solver': 'SGD with momentum',
            'n_iterations': getattr(model, 'n_iter_', 'N/A'),
            'converged': getattr(model, 'n_iter_', 0) < getattr(model, 'max_iter', 200)
        }
    
    @staticmethod
    def _get_svr_info(model) -> Dict[str, Any]:
        """Get SVR-specific information."""
        return {
            'C': getattr(model, 'C', 'N/A'),
            'epsilon': getattr(model, 'epsilon', 'N/A') if hasattr(model, 'epsilon') else 'N/A',
            'nu': getattr(model, 'nu', 'N/A') if hasattr(model, 'nu') else 'N/A',
            'kernel': getattr(model, 'kernel', 'N/A'),
            'gamma': getattr(model, 'gamma', 'N/A'),
            'n_support_vectors': len(getattr(model, 'support_', [])) if hasattr(model, 'support_') else 'N/A'
        }
    
    @staticmethod
    def _get_xgboost_info(model) -> Dict[str, Any]:
        """Get XGBoost-specific information."""
        return {
            'n_estimators': getattr(model, 'n_estimators', 'N/A'),
            'max_depth': getattr(model, 'max_depth', 'N/A'),
            'learning_rate': getattr(model, 'learning_rate', 'N/A'),
            'subsample': getattr(model, 'subsample', 'N/A'),
            'colsample_bytree': getattr(model, 'colsample_bytree', 'N/A'),
            'feature_importances_available': hasattr(model, 'feature_importances_')
        }
    
    @staticmethod
    def calculate_comprehensive_metrics(model, X_train: np.ndarray, y_train: np.ndarray,
                                      X_test: Optional[np.ndarray] = None, 
                                      y_test: Optional[np.ndarray] = None,
                                      cv_folds: int = 5) -> Dict[str, Any]:
        """
        Calculate comprehensive performance metrics.
        
        Parameters
        ----------
        model : fitted model
            The trained model
        X_train, y_train : arrays
            Training data
        X_test, y_test : arrays, optional
            Test data
        cv_folds : int
            Number of cross-validation folds
            
        Returns
        -------
        metrics : dict
            Comprehensive performance metrics
        """
        metrics = {}
        
        # Training metrics
        y_train_pred = model.predict(X_train)
        metrics['train_r2'] = r2_score(y_train, y_train_pred)
        metrics['train_rmse'] = np.sqrt(mean_squared_error(y_train, y_train_pred))
        metrics['train_mae'] = np.mean(np.abs(y_train - y_train_pred))
        
        # Cross-validation metrics
        try:
            cv_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, 
                                      scoring='neg_mean_squared_error')
            metrics['cv_rmse'] = np.sqrt(-cv_scores.mean())
            metrics['cv_rmse_std'] = np.sqrt(cv_scores.std())
            
            cv_r2_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, 
                                         scoring='r2')
            metrics['cv_r2'] = cv_r2_scores.mean()
            metrics['cv_r2_std'] = cv_r2_scores.std()
        except Exception as e:
            metrics['cv_rmse'] = 'N/A'
            metrics['cv_r2'] = 'N/A'
            metrics['cv_error'] = str(e)
        
        # Test metrics (if available)
        if X_test is not None and y_test is not None:
            y_test_pred = model.predict(X_test)
            metrics['test_r2'] = r2_score(y_test, y_test_pred)
            metrics['test_rmse'] = np.sqrt(mean_squared_error(y_test, y_test_pred))
            metrics['test_mae'] = np.mean(np.abs(y_test - y_test_pred))
            
            # Prediction interval metrics
            residuals = y_test - y_test_pred
            metrics['test_bias'] = np.mean(residuals)
            metrics['test_sep'] = np.sqrt(np.mean((residuals - metrics['test_bias'])**2))
        
        return metrics
    
    @staticmethod
    def generate_model_summary(model, algorithm_name: str, metrics: Dict[str, Any],
                             parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate comprehensive model summary.
        
        Parameters
        ----------
        model : fitted model
            The trained model
        algorithm_name : str
            Name of the algorithm
        metrics : dict
            Performance metrics
        parameters : dict
            Model parameters
            
        Returns
        -------
        summary : dict
            Comprehensive model summary
        """
        # Get algorithm-specific info
        algo_info = ModelDiagnostics.get_algorithm_info(model, algorithm_name)
        
        # Combine all information
        summary = {
            'algorithm_info': algo_info,
            'performance_metrics': metrics,
            'parameters': parameters,
            'model_quality': ModelDiagnostics._assess_model_quality(metrics),
            'recommendations': ModelDiagnostics._generate_recommendations(algo_info, metrics)
        }
        
        return summary
    
    @staticmethod
    def _assess_model_quality(metrics: Dict[str, Any]) -> Dict[str, str]:
        """Assess overall model quality based on metrics."""
        quality = {}
        
        # R² assessment
        train_r2 = metrics.get('train_r2', 0)
        cv_r2 = metrics.get('cv_r2', 0)
        
        if isinstance(train_r2, (int, float)) and isinstance(cv_r2, (int, float)):
            if cv_r2 > 0.9:
                quality['overall'] = 'Excellent'
            elif cv_r2 > 0.8:
                quality['overall'] = 'Good'
            elif cv_r2 > 0.6:
                quality['overall'] = 'Fair'
            else:
                quality['overall'] = 'Poor'
            
            # Overfitting assessment
            r2_diff = train_r2 - cv_r2
            if r2_diff > 0.2:
                quality['overfitting'] = 'High risk'
            elif r2_diff > 0.1:
                quality['overfitting'] = 'Moderate risk'
            else:
                quality['overfitting'] = 'Low risk'
        else:
            quality['overall'] = 'Unable to assess'
            quality['overfitting'] = 'Unable to assess'
        
        return quality
    
    @staticmethod
    def _generate_recommendations(algo_info: Dict[str, Any], 
                                metrics: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on model performance."""
        recommendations = []
        
        # Performance-based recommendations
        cv_r2 = metrics.get('cv_r2', 0)
        if isinstance(cv_r2, (int, float)):
            if cv_r2 < 0.6:
                recommendations.append("Consider trying different preprocessing methods")
                recommendations.append("Evaluate variable selection results")
                recommendations.append("Try different algorithm parameters")
        
        # Algorithm-specific recommendations
        algorithm = algo_info.get('algorithm', '')
        
        if 'NIPALS' in algorithm:
            converged = algo_info.get('converged', True)
            if not converged:
                recommendations.append("NIPALS did not converge - consider increasing max_iter or adjusting tolerance")
        
        elif 'ANN' in algorithm or 'MLP' in algorithm:
            converged = algo_info.get('converged', True)
            if not converged:
                recommendations.append("Neural network did not converge - consider increasing max_iter or adjusting learning rate")
            
            n_iter = algo_info.get('n_iterations', 0)
            if isinstance(n_iter, int) and n_iter < 50:
                recommendations.append("Neural network converged very quickly - consider increasing complexity")
        
        if not recommendations:
            recommendations.append("Model performance looks good - proceed to next step")
        
        return recommendations

#!/usr/bin/env python3
"""
Test script to verify all algorithms in Step 5 are working correctly.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split

# Import the algorithms
from utils.nipals_pls import NIPALSRegression
from utils.enhanced_ann import EnhancedANN
from sklearn.cross_decomposition import PLSRegression
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR, NuSVR

# Optional XGBoost
try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False

def create_test_data():
    """Create synthetic spectroscopic-like data for testing."""
    # Create regression data similar to spectroscopic data
    X, y = make_regression(
        n_samples=100,
        n_features=50,  # Like wavelengths
        n_informative=20,
        noise=0.1,
        random_state=42
    )
    
    # Split into train/test
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42
    )
    
    return X_train, X_test, y_train, y_test

def test_algorithm(name, model, X_train, X_test, y_train, y_test):
    """Test a single algorithm."""
    print(f"\n🧪 Testing {name}...")
    
    try:
        # Fit the model
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred_train = model.predict(X_train)
        y_pred_test = model.predict(X_test)
        
        # Calculate metrics
        from sklearn.metrics import mean_squared_error, r2_score
        
        train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
        test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
        train_r2 = r2_score(y_train, y_pred_train)
        test_r2 = r2_score(y_test, y_pred_test)
        
        print(f"   ✅ SUCCESS")
        print(f"   📊 Train RMSE: {train_rmse:.4f}, R²: {train_r2:.4f}")
        print(f"   📊 Test RMSE:  {test_rmse:.4f}, R²: {test_r2:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ FAILED: {str(e)}")
        return False

def main():
    """Run algorithm tests."""
    print("🔬 MELK Chemo Copilot - Algorithm Testing")
    print("=" * 50)
    
    # Create test data
    print("📊 Creating test data...")
    X_train, X_test, y_train, y_test = create_test_data()
    print(f"   Training data: {X_train.shape}")
    print(f"   Test data: {X_test.shape}")
    
    # Test algorithms
    algorithms = []
    
    # 1. NIPALS PLS
    algorithms.append(("NIPALS (PLS1, PLS2)", NIPALSRegression(n_components=5)))
    
    # 2. SIMPLS
    algorithms.append(("SIMPLS", PLSRegression(n_components=5)))
    
    # 3. Enhanced ANN
    algorithms.append(("Enhanced ANN (MATLAB-style)", EnhancedANN(
        hidden_layer_sizes=(20,),
        activation='tanh',
        solver='lbfgs',
        max_iter=300
    )))
    
    # 4. Standard MLP
    algorithms.append(("Multilayer Perceptron (MLP)", MLPRegressor(
        hidden_layer_sizes=(50,),
        activation='relu',
        solver='adam',
        max_iter=300,
        random_state=42
    )))
    
    # 5. ε-SVR
    algorithms.append(("ε-Support Vector Regression (ε-SVR)", SVR(
        C=1.0,
        epsilon=0.1,
        kernel='rbf'
    )))
    
    # 6. Nu-SVR
    algorithms.append(("Nu-Support Vector Regression (Nu-SVR)", NuSVR(
        C=1.0,
        nu=0.5,
        kernel='rbf'
    )))
    
    # 7. XGBoost (if available)
    if HAS_XGBOOST:
        algorithms.append(("XGBoost", xgb.XGBRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )))
    else:
        print("\n⚠️  XGBoost not available - skipping")
    
    # Run tests
    results = {}
    for name, model in algorithms:
        results[name] = test_algorithm(name, model, X_train, X_test, y_train, y_test)
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {name}")
    
    print(f"\n🎯 Results: {passed}/{total} algorithms working")
    
    if passed == total:
        print("🎉 All algorithms are working correctly!")
        return 0
    else:
        print("⚠️  Some algorithms failed - check the errors above")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

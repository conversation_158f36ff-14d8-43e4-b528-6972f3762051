"""
Step 1: Data Upload for MELK Chemo Copilot
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Optional, Tuple
from steps.base_step import BaseStep
from ui.components import UIComponents
from config.settings import UPLOAD_SETTINGS


class DataUploadStep(BaseStep):
    """Step 1: Data Upload and Initial Validation"""

    def __init__(self):
        super().__init__(1, "Data Upload")

    def render(self) -> None:
        """Render the data upload step."""
        self.render_header()

        # Only show training data upload section
        self._render_training_data_section()

        # Show navigation if training data is loaded
        if self.session.has("x_train") and self.session.has("y_train"):
            self._render_navigation_section()

    def _render_training_data_section(self) -> None:
        """Render the training data upload section."""
        st.markdown("## 📊 Training Data Upload")

        # Check if training data is already loaded
        if self.session.has("x_train") and self.session.has("y_train"):
            st.success("✅ Training data is already loaded!")

            x_train = self.session.get("x_train")
            y_train = self.session.get("y_train")

            # Show training data summary
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Training Samples", x_train.shape[0])
            with col2:
                st.metric("Spectral Variables", x_train.shape[1])
            with col3:
                st.metric("Target Variables", y_train.shape[1])

            # Data preview
            with st.expander("📋 Training Data Preview", expanded=False):
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown("#### X Training Data (First 5 rows)")
                    st.dataframe(x_train.head(), use_container_width=True)
                with col2:
                    st.markdown("#### Y Training Data (First 5 rows)")
                    st.dataframe(y_train.head(), use_container_width=True)

            # Option to reload training data
            if st.button("🔄 Upload Different Training Data", key="reload_training_data"):
                self.session.remove("x_train")
                self.session.remove("y_train")
                st.rerun()
        else:
            st.info("Upload your spectroscopic training data to begin the chemometric analysis workflow.")

            # Information about test data
            st.info("💡 **Note**: Test data for model validation can be uploaded later in Step 6: Model Prediction.")

            # Create tabs for different upload options
            tab1, tab2 = st.tabs(["📁 Upload Files", "📊 Use Sample Data"])

            with tab1:
                self._render_training_file_upload()

            with tab2:
                self._render_sample_data_option()

    def _render_training_file_upload(self) -> None:
        """Render training data file upload interface."""
        st.markdown("#### Upload Training Data Files")

        # File format information
        with st.expander("📋 File Format Requirements", expanded=False):
            st.markdown("""
            **Supported Formats:** CSV (.csv), Excel (.xlsx, .xls)

            **Data Structure Requirements:**
            - **Samples as rows, variables as columns**
            - **First row should contain variable names (headers)**
            - **First column should contain sample names (will be used as index)**
            - **All data should be numeric (except headers and sample names)**
            - **No missing values allowed**

            **Example Structure:**
            ```
            Sample_Name    Wavelength_1000    Wavelength_1001    ...
            Sample_001     0.234              0.245              ...
            Sample_002     0.198              0.201              ...
            ```
            """)

        # Training data upload form
        with st.form("training_data_upload_form", clear_on_submit=False):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**X Training Data (Spectra)**")
                x_train_file = st.file_uploader(
                    "Upload spectroscopic data",
                    type=['csv', 'xlsx', 'xls'],
                    help="Spectroscopic data: samples as rows, wavelengths/variables as columns",
                    key="x_train_upload_form"
                )

            with col2:
                st.markdown("**Y Training Data (Concentrations)**")
                y_train_file = st.file_uploader(
                    "Upload concentration data",
                    type=['csv', 'xlsx', 'xls'],
                    help="Concentration data: samples as rows, analytes/targets as columns",
                    key="y_train_upload_form"
                )

            # Submit button
            submitted = st.form_submit_button("Upload Training Data", type="primary")

            if submitted:
                if x_train_file is not None and y_train_file is not None:
                    try:
                        # Read training data
                        x_train_df = self._read_uploaded_file(x_train_file)
                        y_train_df = self._read_uploaded_file(y_train_file)

                        # Process the training data
                        self._process_training_data(x_train_df, y_train_df)

                    except Exception as e:
                        st.error(f"Error reading uploaded files: {str(e)}")
                else:
                    st.error("Please upload both X training and Y training files before submitting.")

    def _read_uploaded_file(self, uploaded_file) -> pd.DataFrame:
        """Read an uploaded file and return as DataFrame."""
        try:
            file_name = uploaded_file.name.lower()

            if file_name.endswith('.csv'):
                # Try different encodings for CSV files
                try:
                    df = pd.read_csv(uploaded_file, index_col=0, encoding='utf-8')
                except UnicodeDecodeError:
                    uploaded_file.seek(0)  # Reset file pointer
                    try:
                        df = pd.read_csv(uploaded_file, index_col=0, encoding='latin-1')
                    except UnicodeDecodeError:
                        uploaded_file.seek(0)  # Reset file pointer
                        df = pd.read_csv(uploaded_file, index_col=0, encoding='cp1252')

            elif file_name.endswith('.xlsx'):
                # Use openpyxl engine for .xlsx files
                df = pd.read_excel(uploaded_file, index_col=0, engine='openpyxl')

            elif file_name.endswith('.xls'):
                # Use xlrd engine for .xls files
                try:
                    df = pd.read_excel(uploaded_file, index_col=0, engine='xlrd')
                except Exception:
                    # Fallback to openpyxl if xlrd fails
                    uploaded_file.seek(0)
                    df = pd.read_excel(uploaded_file, index_col=0, engine='openpyxl')

            else:
                raise ValueError(f"Unsupported file format: {uploaded_file.name}. Supported formats: .csv, .xlsx, .xls")

            # Basic validation
            if df.empty:
                raise ValueError("The uploaded file is empty")

            # Check for reasonable data dimensions
            if df.shape[0] < 2:
                raise ValueError("File must contain at least 2 rows of data")

            if df.shape[1] < 1:
                raise ValueError("File must contain at least 1 column of data")

            # Ensure column names are strings to avoid mixed type issues
            df.columns = df.columns.astype(str)

            # Ensure index is string type too
            df.index = df.index.astype(str)

            return df

        except Exception as e:
            error_msg = str(e)

            # Provide more helpful error messages
            if "openpyxl" in error_msg:
                error_msg = "Excel file support requires openpyxl. Please install it or use CSV format."
            elif "xlrd" in error_msg:
                error_msg = "Old Excel file (.xls) support requires xlrd. Please install it or convert to .xlsx/.csv format."
            elif "UnicodeDecodeError" in error_msg:
                error_msg = "File encoding issue. Please save your CSV file with UTF-8 encoding."
            elif "ParserError" in error_msg:
                error_msg = "File format error. Please check that your file is properly formatted."

            raise Exception(f"Error reading {uploaded_file.name}: {error_msg}")



    def _render_sample_data_option(self) -> None:
        """Render sample data loading option."""
        st.markdown("#### Use Built-in Sample Data")
        st.info("Load pre-configured sample data to explore the application features.")

        if st.button("Load Sample Data", type="primary", key="load_sample_data"):
            self._load_sample_data()

    def _process_training_data(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Process and validate training data."""

        # Validate training data
        validation_results = self._validate_training_data(x_train, y_train)

        # Display validation results
        st.markdown("### 🔍 Training Data Validation Results")
        all_valid = True

        for check, result in validation_results.items():
            if result:
                st.success(f"✅ {check}")
            else:
                st.error(f"❌ {check}")
                all_valid = False

        if not all_valid:
            st.error("Please fix the data validation issues before proceeding.")
            return

        # Store training data in session state
        self.session.set("x_train", x_train)
        self.session.set("y_train", y_train)

        st.success("✅ Training data uploaded successfully!")
        st.rerun()

    def _validate_training_data(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> dict:
        """Validate training data."""
        validation = {}

        # Check basic requirements
        validation["X_train has data"] = not x_train.empty
        validation["Y_train has data"] = not y_train.empty
        validation["X_train is numeric"] = x_train.select_dtypes(include=[np.number]).shape[1] > 0
        validation["Y_train is numeric"] = y_train.select_dtypes(include=[np.number]).shape[1] > 0

        # Check sample alignment
        validation["Sample count matches (X_train vs Y_train)"] = x_train.shape[0] == y_train.shape[0]

        # Check for missing values
        validation["X_train has no missing values"] = not x_train.isnull().any().any()
        validation["Y_train has no missing values"] = not y_train.isnull().any().any()

        return validation





    def _render_data_summary(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render summary of uploaded training data."""
        st.markdown("### 📊 Training Data Summary")

        # Training data metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Training Samples", x_train.shape[0])
        with col2:
            st.metric("Spectral Variables", x_train.shape[1])
        with col3:
            st.metric("Target Variables", y_train.shape[1])

        # Data preview
        with st.expander("📋 Training Data Preview", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### X Training Data (First 5 rows)")
                st.dataframe(x_train.head(), use_container_width=True)

            with col2:
                st.markdown("#### Y Training Data (First 5 rows)")
                st.dataframe(y_train.head(), use_container_width=True)



    def _render_navigation_section(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        clicked = self.render_navigation_buttons(
            show_previous=False,
            next_enabled=True,
            custom_next_text="Next: Data Overview →"
        )

        self.handle_navigation(clicked)

    def _load_sample_data(self) -> None:
        """Load sample data for demonstration."""
        try:
            # Generate sample spectroscopic data
            np.random.seed(42)
            n_samples = 50
            n_wavelengths = 200
            n_analytes = 2

            # Generate synthetic NIR spectra
            wavelengths = np.linspace(1000, 2500, n_wavelengths)

            # Create base spectra with some realistic features
            x_train = np.random.normal(0.5, 0.1, (n_samples, n_wavelengths))

            # Add some spectral features
            for i in range(n_samples):
                # Add baseline drift
                baseline = np.linspace(0.1, 0.3, n_wavelengths) * np.random.normal(1, 0.1)
                x_train[i] += baseline

                # Add some peaks
                for peak_center in [1200, 1450, 1900, 2100]:
                    peak_idx = np.argmin(np.abs(wavelengths - peak_center))
                    peak_width = 20
                    peak_height = np.random.normal(0.2, 0.05)

                    for j in range(max(0, peak_idx - peak_width),
                                 min(n_wavelengths, peak_idx + peak_width)):
                        distance = abs(j - peak_idx)
                        x_train[i, j] += peak_height * np.exp(-distance**2 / (2 * (peak_width/3)**2))

            # Generate corresponding concentrations
            y_train = np.random.uniform(0, 100, (n_samples, n_analytes))

            # Create DataFrames with string column names
            x_train_df = pd.DataFrame(x_train, columns=[f"Wavelength_{w:.1f}" for w in wavelengths])
            y_train_df = pd.DataFrame(y_train, columns=["Analyte_1", "Analyte_2"])

            # Ensure column names are strings
            x_train_df.columns = x_train_df.columns.astype(str)
            y_train_df.columns = y_train_df.columns.astype(str)

            # Add sample names as strings
            x_train_df.index = [f"Sample_{i+1:03d}" for i in range(n_samples)]
            y_train_df.index = [f"Sample_{i+1:03d}" for i in range(n_samples)]
            x_train_df.index = x_train_df.index.astype(str)
            y_train_df.index = y_train_df.index.astype(str)

            # Process the sample training data
            self._process_training_data(x_train_df, y_train_df)

        except Exception as e:
            st.error(f"Error loading sample data: {str(e)}")

    def validate_step_completion(self) -> bool:
        """Validate that required data is uploaded."""
        return self.session.has("x_train") and self.session.has("y_train")

    def check_prerequisites(self) -> list:
        """No prerequisites for first step."""
        return []

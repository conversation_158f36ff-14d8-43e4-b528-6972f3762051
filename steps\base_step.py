"""
Base Step Class for MELK Chemo Copilot

Provides common functionality for all workflow steps.
"""

import streamlit as st
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from core.session_manager import session_manager


class BaseStep(ABC):
    """Base class for all workflow steps."""

    def __init__(self, step_number: int, step_name: str):
        self.step_number = step_number
        self.step_name = step_name
        self.session = session_manager

    @abstractmethod
    def render(self) -> None:
        """Render the step UI. Must be implemented by subclasses."""
        pass

    def render_header(self) -> None:
        """Render the step header with enhanced styling."""
        st.markdown(f"""
        <div class="step-header">
            <h1>Step {self.step_number}: {self.step_name}</h1>
        </div>
        """, unsafe_allow_html=True)

    def render_navigation_buttons(self,
                                show_previous: bool = True,
                                show_next: bool = True,
                                next_enabled: bool = True,
                                custom_next_text: str = "Next Step →",
                                custom_previous_text: str = "← Previous Step",
                                show_summary: bool = True,
                                summary_data: Dict[str, str] = None) -> Dict[str, bool]:
        """
        Render navigation buttons and return which ones were clicked.

        Parameters:
        -----------
        show_summary : bool
            Whether to show the step summary button
        summary_data : Dict[str, str]
            Dictionary containing 'options_studied', 'selections_made', 'reasoning' for summary generation

        Returns:
            Dict with 'previous' and 'next' keys indicating if buttons were clicked
        """
        clicked = {"previous": False, "next": False}

        # Import here to avoid circular imports
        from utils.chatgpt_helper import ChatGPTHelper

        # Create navigation layout with summary button
        if show_summary and summary_data:
            col1, col2, col3, col4, col5 = st.columns([1, 0.2, 1, 0.2, 1])
        else:
            col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if show_previous and self.step_number > 1:
                if st.button(custom_previous_text, key=f"step_{self.step_number}_prev"):
                    clicked["previous"] = True

        # Summary button (if enabled and data provided)
        if show_summary and summary_data:
            with col3:
                summary_button_html = ChatGPTHelper.create_step_summary_button(
                    step_number=self.step_number,
                    step_name=self.step_name,
                    options_studied=summary_data.get('options_studied', ''),
                    selections_made=summary_data.get('selections_made', ''),
                    reasoning=summary_data.get('reasoning', ''),
                    button_text="📋 Step Summary"
                )
                st.markdown(summary_button_html, unsafe_allow_html=True)

        # Next button
        next_col = col5 if (show_summary and summary_data) else col3
        with next_col:
            if show_next and self.step_number < 7:  # Updated to Step 7 as final step
                if st.button(
                    custom_next_text,
                    key=f"step_{self.step_number}_next",
                    disabled=not next_enabled
                ):
                    clicked["next"] = True

        return clicked

    def handle_navigation(self, clicked: Dict[str, bool]) -> None:
        """Handle navigation button clicks."""
        if clicked["previous"]:
            self.session.set_current_step(self.step_number - 1)
            st.rerun()
        elif clicked["next"]:
            if self.validate_step_completion():
                self.session.set_current_step(self.step_number + 1)
                st.rerun()
            else:
                st.error("Please complete all required fields before proceeding.")

    def validate_step_completion(self) -> bool:
        """
        Validate that the step is complete and ready to proceed.
        Override in subclasses for step-specific validation.
        """
        return True

    def get_step_data(self) -> Dict[str, Any]:
        """Get data specific to this step from session state."""
        return {}

    def save_step_data(self, data: Dict[str, Any]) -> None:
        """Save step-specific data to session state."""
        for key, value in data.items():
            self.session.set(key, value)

    def render_info_box(self, title: str, content: str, type: str = "info") -> None:
        """Render an information box."""
        if type == "info":
            st.info(f"**{title}**\n\n{content}")
        elif type == "warning":
            st.warning(f"**{title}**\n\n{content}")
        elif type == "error":
            st.error(f"**{title}**\n\n{content}")
        elif type == "success":
            st.success(f"**{title}**\n\n{content}")

    def render_help_section(self, help_content: str) -> None:
        """Render a collapsible help section."""
        with st.expander("ℹ️ Help & Information"):
            st.markdown(help_content)

    def render_step_summary(self, summary_data: Dict[str, Any]) -> None:
        """Render a summary of current step selections."""
        if summary_data:
            st.markdown("### Current Selections")

            for key, value in summary_data.items():
                if isinstance(value, dict):
                    st.markdown(f"**{key}:**")
                    for sub_key, sub_value in value.items():
                        st.markdown(f"  - {sub_key}: {sub_value}")
                else:
                    st.markdown(f"**{key}:** {value}")

    def render_progress_indicator(self, current_progress: float, total_progress: float = 1.0) -> None:
        """Render a progress indicator for the current step."""
        progress_percentage = current_progress / total_progress
        st.progress(progress_percentage)
        st.markdown(f"Progress: {current_progress:.1f}/{total_progress:.1f}")

    def check_prerequisites(self) -> List[str]:
        """
        Check if prerequisites for this step are met.
        Returns list of missing prerequisites.
        """
        return []

    def render_prerequisites_warning(self) -> bool:
        """
        Render warning if prerequisites are not met.
        Returns True if prerequisites are met, False otherwise.
        """
        missing = self.check_prerequisites()
        if missing:
            st.error(
                f"**Prerequisites not met for Step {self.step_number}:**\n\n" +
                "\n".join([f"• {item}" for item in missing]) +
                "\n\nPlease complete the previous steps first."
            )
            return False
        return True

    def render_data_validation_results(self, validation_results: Dict[str, bool]) -> bool:
        """
        Render data validation results.
        Returns True if all validations pass.
        """
        all_valid = all(validation_results.values())

        if not all_valid:
            st.error("**Data Validation Issues:**")
            for check, result in validation_results.items():
                if not result:
                    st.error(f"❌ {check}")
                else:
                    st.success(f"✅ {check}")

        return all_valid

"""
Step 7: Model Report for MELK Chemo Copilot

This step generates comprehensive model development reports.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any, List, Tuple, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import ChatGPTHelper
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')


class Step7ModelPrediction(BaseStep):
    """Step 7: Model Report - Comprehensive model development report."""

    def __init__(self):
        super().__init__(step_number=7, step_name="Model Report")

    def render(self) -> None:
        """Render the model report interface."""
        # Header with help icon
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("## 📊 Step 7: Model Report")
            st.markdown("*Comprehensive report of your complete chemometric model development workflow*")
        with col2:
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Model Development Report",
                "understanding comprehensive chemometric model reports",
                """Please explain the importance of comprehensive model development reports in chemometrics:

1. What should be included in a complete chemometric model report?
2. How to document preprocessing steps and variable selection methods?
3. What validation metrics and plots should be presented?
4. How to interpret and communicate model performance to stakeholders?
5. What are the best practices for reproducible chemometric reporting?

Please provide guidance for creating professional model development reports."""
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Check prerequisites
        if not self._check_prerequisites():
            return

        # Get built model
        built_model = self.session.get("built_model")

        # Display model summary
        self._render_model_summary(built_model)

        # Prediction interface
        self._render_prediction_interface(built_model)

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        if not self.session.has("built_model"):
            missing_items.append("Built model from Step 6")
        if not self.session.has("x_train_selected"):
            missing_items.append("Training data")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 6", key="back_to_step6"):
                self.session.set_current_step(6)
                st.rerun()
            return False

        return True

    def _render_model_summary(self, built_model: Dict[str, Any]) -> None:
        """Render summary of the built model."""
        st.markdown("### 📋 Trained Model Summary")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Algorithm", built_model["algorithm"])
        with col2:
            st.metric("CV RMSE", f"{built_model['cv_rmse']:.4f}")
        with col3:
            st.metric("CV R²", f"{built_model['cv_r2']:.4f}")
        with col4:
            st.metric("Training Samples", built_model['training_data_shape'][0])

    def _render_prediction_interface(self, built_model: Dict[str, Any]) -> None:
        """Render the prediction interface."""
        st.markdown("### 🎯 Model Prediction")

        # Check for test data
        has_test_data = self.session.has("x_test_selected") and self.session.has("y_test")

        if has_test_data:
            # Test data prediction
            self._render_test_data_prediction(built_model)
        else:
            st.info("ℹ️ No test data available. You can still use the model for prediction on unknown samples.")

        # Unknown sample prediction
        self._render_unknown_sample_prediction(built_model)

        # Navigation
        self._render_navigation()

    def _render_test_data_prediction(self, built_model: Dict[str, Any]) -> None:
        """Render test data prediction interface."""
        st.markdown("#### 📊 Test Data Validation")

        if not self.session.has("test_predictions"):
            st.info("Validate your model performance using the test data.")

            if st.button("🧪 Predict Test Data", type="primary", key="predict_test_data"):
                self._predict_test_data(built_model)
        else:
            # Show test prediction results
            self._render_test_prediction_results()

    def _predict_test_data(self, built_model: Dict[str, Any]) -> None:
        """Predict on test data."""
        with st.spinner("🧪 Predicting test data..."):
            try:
                x_test = self.session.get("x_test_selected")
                y_test = self.session.get("y_test")
                model = built_model["model"]

                # Convert to numpy arrays
                x_test_array = x_test.select_dtypes(include=[np.number]).values
                y_test_array = y_test.select_dtypes(include=[np.number]).values

                if y_test_array.ndim == 2 and y_test_array.shape[1] == 1:
                    y_test_array = y_test_array.ravel()

                # Make predictions
                y_pred = model.predict(x_test_array)

                # Calculate metrics
                test_rmse = np.sqrt(mean_squared_error(y_test_array, y_pred))
                test_r2 = r2_score(y_test_array, y_pred)

                # Store results
                test_results = {
                    "y_true": y_test_array,
                    "y_pred": y_pred,
                    "test_rmse": test_rmse,
                    "test_r2": test_r2,
                    "sample_names": x_test.index.tolist() if hasattr(x_test, 'index') else list(range(len(x_test)))
                }

                self.session.set("test_predictions", test_results)
                st.success("✅ Test data prediction completed!")
                st.rerun()

            except Exception as e:
                st.error(f"❌ Error predicting test data: {str(e)}")

    def _render_test_prediction_results(self) -> None:
        """Render test prediction results."""
        test_results = self.session.get("test_predictions")

        st.markdown("##### 📈 Test Prediction Results")

        # Metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Test RMSE", f"{test_results['test_rmse']:.4f}")
        with col2:
            st.metric("Test R²", f"{test_results['test_r2']:.4f}")
        with col3:
            st.metric("Test Samples", len(test_results['y_true']))

        # Prediction plot
        self._create_prediction_plot(test_results)

        # Prediction table
        self._create_prediction_table(test_results)

    def _create_prediction_plot(self, test_results: Dict[str, Any]) -> None:
        """Create prediction vs actual plot."""
        y_true = test_results['y_true']
        y_pred = test_results['y_pred']

        fig = go.Figure()

        # Scatter plot
        fig.add_trace(go.Scatter(
            x=y_true,
            y=y_pred,
            mode='markers',
            name='Predictions',
            marker=dict(
                size=8,
                color='rgba(25, 118, 210, 0.7)',
                line=dict(width=1, color='rgba(25, 118, 210, 1)')
            ),
            text=test_results['sample_names'],
            hovertemplate='<b>%{text}</b><br>Actual: %{x:.3f}<br>Predicted: %{y:.3f}<extra></extra>'
        ))

        # Perfect prediction line
        min_val = min(min(y_true), min(y_pred))
        max_val = max(max(y_true), max(y_pred))
        fig.add_trace(go.Scatter(
            x=[min_val, max_val],
            y=[min_val, max_val],
            mode='lines',
            name='Perfect Prediction',
            line=dict(color='red', dash='dash', width=2)
        ))

        fig.update_layout(
            title=f"Predicted vs Actual Values (R² = {test_results['test_r2']:.3f})",
            xaxis_title="Actual Values",
            yaxis_title="Predicted Values",
            font=dict(family="Nunito Sans", size=12),
            showlegend=True,
            height=500
        )

        st.plotly_chart(fig, use_container_width=True)

    def _create_prediction_table(self, test_results: Dict[str, Any]) -> None:
        """Create prediction results table."""
        st.markdown("##### 📋 Detailed Prediction Results")

        # Create DataFrame
        results_df = pd.DataFrame({
            'Sample': test_results['sample_names'],
            'Actual': test_results['y_true'],
            'Predicted': test_results['y_pred'],
            'Residual': test_results['y_true'] - test_results['y_pred'],
            'Abs Error': np.abs(test_results['y_true'] - test_results['y_pred'])
        })

        # Format numbers
        results_df['Actual'] = results_df['Actual'].round(4)
        results_df['Predicted'] = results_df['Predicted'].round(4)
        results_df['Residual'] = results_df['Residual'].round(4)
        results_df['Abs Error'] = results_df['Abs Error'].round(4)

        st.dataframe(results_df, use_container_width=True)

        # Download button
        csv = results_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Prediction Results",
            data=csv,
            file_name="test_prediction_results.csv",
            mime="text/csv"
        )

    def _render_unknown_sample_prediction(self, built_model: Dict[str, Any]) -> None:
        """Render unknown sample prediction interface."""
        st.markdown("#### 🔮 Unknown Sample Prediction")
        st.info("Upload new spectroscopic data to predict unknown samples using your trained model.")

        # File upload for unknown samples
        uploaded_file = st.file_uploader(
            "Upload Unknown Sample Data (CSV)",
            type=['csv'],
            help="Upload CSV file with spectroscopic data for unknown samples",
            key="unknown_samples_upload"
        )

        if uploaded_file is not None:
            try:
                # Read uploaded data
                unknown_data = pd.read_csv(uploaded_file, sep=';', decimal=',')

                st.success(f"✅ Loaded {len(unknown_data)} unknown samples")
                st.write("**Data Preview:**")
                st.dataframe(unknown_data.head(), use_container_width=True)

                # Predict button
                if st.button("🔮 Predict Unknown Samples", type="primary", key="predict_unknown"):
                    self._predict_unknown_samples(built_model, unknown_data)

            except Exception as e:
                st.error(f"❌ Error loading unknown sample data: {str(e)}")

    def _predict_unknown_samples(self, built_model: Dict[str, Any], unknown_data: pd.DataFrame) -> None:
        """Predict unknown samples."""
        with st.spinner("🔮 Predicting unknown samples..."):
            try:
                model = built_model["model"]

                # Convert to numpy array
                x_unknown = unknown_data.select_dtypes(include=[np.number]).values

                # Make predictions
                predictions = model.predict(x_unknown)

                # Create results DataFrame
                results_df = pd.DataFrame({
                    'Sample': unknown_data.index + 1,
                    'Predicted_Value': predictions.round(4)
                })

                st.success("✅ Unknown sample prediction completed!")
                st.markdown("##### 🔮 Unknown Sample Predictions")
                st.dataframe(results_df, use_container_width=True)

                # Download button
                csv = results_df.to_csv(index=False)
                st.download_button(
                    label="📥 Download Unknown Sample Predictions",
                    data=csv,
                    file_name="unknown_sample_predictions.csv",
                    mime="text/csv"
                )

            except Exception as e:
                st.error(f"❌ Error predicting unknown samples: {str(e)}")

    def _render_navigation(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Model Prediction", key="back_to_step6_from_report"):
                self.session.set_current_step(6)
                st.rerun()

        with col3:
            if st.button("🎉 Complete Workflow", type="primary", key="complete_workflow"):
                st.success("🎉 Workflow completed successfully!")
                st.balloons()
                # Mark workflow as complete
                self.session.set("workflow_complete", True)

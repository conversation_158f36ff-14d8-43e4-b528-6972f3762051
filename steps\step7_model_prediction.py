"""
Step 7: Model Report for MELK Chemo Copilot

This step generates comprehensive model development reports.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, Any, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import ChatGPTHelper
from sklearn.metrics import mean_squared_error, r2_score
from datetime import datetime
import warnings
import io
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
warnings.filterwarnings('ignore')


class Step7ModelPrediction(BaseStep):
    """Step 7: Model Report - Comprehensive model development report."""

    def __init__(self):
        super().__init__(step_number=7, step_name="Model Report")

    def render(self) -> None:
        """Render the comprehensive model development report."""
        try:
            # Header with help icon
            col1, col2 = st.columns([4, 1])
            with col1:
                st.markdown("## 📊 Step 7: Comprehensive Model Development Report")
                st.markdown("*Complete documentation of your chemometric model development workflow*")
            with col2:
                help_icon_html = ChatGPTHelper.create_inline_help_icon(
                    "Model Development Report",
                    "understanding comprehensive chemometric model reports",
                    """Please explain the importance of comprehensive model development reports in chemometrics:

1. What should be included in a complete chemometric model report?
2. How to document preprocessing steps and variable selection methods?
3. What validation metrics and plots should be presented?
4. How to interpret and communicate model performance to stakeholders?
5. What are the best practices for reproducible chemometric reporting?

Please provide guidance for creating professional model development reports."""
                )
                st.markdown(help_icon_html, unsafe_allow_html=True)

            # Check prerequisites
            if not self._check_prerequisites():
                return

            # Get trained model from Step 5 session data
            built_model = self._get_trained_model()

            if not built_model:
                st.error("❌ No trained model found. Please complete Step 5 first.")
                return

            # Generate comprehensive report
            self._render_comprehensive_report(built_model)

        except Exception as e:
            st.error(f"❌ Error in Step 7 render: {str(e)}")
            st.write("**Error Details:**")
            import traceback
            st.code(traceback.format_exc())

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        # Check if we have either final model config or selected nomination
        has_model = (self.session.has("final_model_config") or
                    self.session.has("selected_final_nomination") or
                    self.session.has("model_nominations"))

        if not has_model:
            missing_items.append("Trained model from Step 5")
        if not self.session.has("x_train_selected"):
            missing_items.append("Training data")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 5", key="back_to_step5"):
                self.session.set_current_step(5)
                st.rerun()
            return False

        return True

    def _get_trained_model(self) -> Optional[Dict[str, Any]]:
        """Get the trained model from Step 5 session data."""
        try:
            # Try to get from selected nomination first
            selected_nomination = self.session.get("selected_final_nomination")
            if selected_nomination and isinstance(selected_nomination, dict):
                return {
                    "model": selected_nomination.get("trained_model"),
                    "algorithm": selected_nomination.get("algorithm", "Unknown"),
                    "parameters": selected_nomination.get("model_params", {}),
                    "rmsecv": selected_nomination.get("overall_rmsecv", 0),
                    "r2": selected_nomination.get("overall_r2", 0),
                    "compound_results": selected_nomination.get("compound_results", {})
                }

            # Try to get from final model config
            final_config = self.session.get("final_model_config")
            if final_config and isinstance(final_config, dict):
                results = final_config.get("results", {})
                if isinstance(results, dict):
                    return {
                        "model": self.session.get("final_model"),
                        "algorithm": final_config.get("algorithm", "Unknown"),
                        "parameters": final_config.get("model_params", {}),
                        "rmsecv": results.get("rmsecv", results.get("best_rmsecv", 0)),
                        "r2": results.get("r2", results.get("train_r2", 0)),
                        "compound_results": results.get("component_results", {})
                    }

            # Try to get from model training results (fallback)
            model_results = self.session.get("model_training_results")
            if model_results and isinstance(model_results, dict):
                results = model_results.get("results", {})
                if isinstance(results, dict):
                    return {
                        "model": results.get("best_model", results.get("model")),
                        "algorithm": model_results.get("algorithm", "Unknown"),
                        "parameters": model_results.get("model_params", {}),
                        "rmsecv": results.get("rmsecv", results.get("best_rmsecv", 0)),
                        "r2": results.get("r2", results.get("train_r2", 0)),
                        "compound_results": results.get("component_results", {})
                    }

        except Exception as e:
            st.error(f"❌ Error retrieving trained model: {str(e)}")

        return None

    def _render_comprehensive_report(self, built_model: Dict[str, Any]) -> None:
        """Render comprehensive model development report."""
        # Report timestamp
        report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        st.markdown(f"*Report generated on: {report_time}*")

        # Create tabs for different report sections
        tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
            "📋 Executive Summary",
            "📊 Data Overview",
            "⚙️ Preprocessing & Variables",
            "🤖 Model Development",
            "📈 Performance & Validation",
            "🎯 Predictions & Results"
        ])

        with tab1:
            self._render_executive_summary(built_model)

        with tab2:
            self._render_data_overview()

        with tab3:
            self._render_preprocessing_and_variables()

        with tab4:
            self._render_model_development(built_model)

        with tab5:
            self._render_performance_validation(built_model)

        with tab6:
            self._render_predictions_and_results(built_model)

        # Final navigation and download
        self._render_final_actions()

    def _render_executive_summary(self, built_model: Dict[str, Any]) -> None:
        """Render executive summary section."""
        st.markdown("### 🎯 Executive Summary")

        # Key metrics overview
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Algorithm", built_model["algorithm"])
        with col2:
            st.metric("Cross-Validation RMSE", f"{built_model['rmsecv']:.4f}")
        with col3:
            st.metric("Training R²", f"{built_model['r2']:.4f}")
        with col4:
            # Get training data info
            x_train = self.session.get("x_train_selected")
            training_samples = len(x_train) if x_train is not None else "Unknown"
            st.metric("Training Samples", training_samples)

        # Model performance assessment
        r2_value = built_model['r2']
        if r2_value > 0.9:
            performance = "Excellent"
            performance_color = "🟢"
        elif r2_value > 0.8:
            performance = "Good"
            performance_color = "🟡"
        elif r2_value > 0.7:
            performance = "Moderate"
            performance_color = "🟠"
        else:
            performance = "Poor"
            performance_color = "🔴"

        st.markdown(f"""
        #### {performance_color} Model Performance Assessment: **{performance}**

        A **{built_model['algorithm']}** model was successfully developed using a {training_samples}-sample training dataset.
        The model achieved a cross-validation RMSE of **{built_model['rmsecv']:.4f}** and R² of **{built_model['r2']:.4f}**,
        indicating **{performance.lower()}** predictive performance.

        The model development followed a systematic workflow including:
        - Data preprocessing and quality control
        - Variable selection optimization
        - Model selection with cross-validation
        - Final model validation and testing
        """)

        # Individual compound results if available
        compound_results = built_model.get("compound_results", {})
        if compound_results and isinstance(compound_results, dict):
            st.markdown("#### 📊 Individual Compound Performance")

            # Create performance table
            compound_data = []
            for compound, results in compound_results.items():
                if isinstance(results, dict):
                    compound_data.append({
                        'Compound': compound,
                        'RMSECV': f"{results.get('rmsecv', 0):.4f}",
                        'R²': f"{results.get('r2', 0):.4f}",
                        'Performance': self._assess_performance(results.get('r2', 0))
                    })

            if compound_data:
                df_compounds = pd.DataFrame(compound_data)
                st.dataframe(df_compounds, use_container_width=True)

    def _assess_performance(self, r2: float) -> str:
        """Assess performance based on R² value."""
        if r2 > 0.9:
            return "🟢 Excellent"
        elif r2 > 0.8:
            return "🟡 Good"
        elif r2 > 0.7:
            return "🟠 Moderate"
        else:
            return "🔴 Poor"

    def _render_data_overview(self) -> None:
        """Render data overview section."""
        st.markdown("### 📊 Dataset Overview")

        # Training data information
        x_train = self.session.get("x_train")
        y_train = self.session.get("y_train")
        x_test = self.session.get("x_test")
        y_test = self.session.get("y_test")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🔬 Training Data")
            if x_train is not None and y_train is not None:
                x_numeric = x_train.select_dtypes(include=[np.number])
                y_numeric = y_train.select_dtypes(include=[np.number])

                st.write(f"**Samples:** {len(x_train)}")
                st.write(f"**Original Variables:** {len(x_numeric.columns)}")
                st.write(f"**Target Compounds:** {len(y_numeric.columns)}")
                st.write(f"**Compound Names:** {', '.join(y_numeric.columns)}")

                # Show data quality metrics
                missing_x = x_numeric.isnull().sum().sum()
                missing_y = y_numeric.isnull().sum().sum()
                st.write(f"**Missing Values (X):** {missing_x}")
                st.write(f"**Missing Values (Y):** {missing_y}")
            else:
                st.warning("Training data not available")

        with col2:
            st.markdown("#### 🧪 Test Data")
            if x_test is not None and y_test is not None:
                x_test_numeric = x_test.select_dtypes(include=[np.number])
                y_test_numeric = y_test.select_dtypes(include=[np.number])

                st.write(f"**Samples:** {len(x_test)}")
                st.write(f"**Variables:** {len(x_test_numeric.columns)}")
                st.write(f"**Target Compounds:** {len(y_test_numeric.columns)}")
                st.write(f"**Compound Names:** {', '.join(y_test_numeric.columns)}")

                # Show data quality metrics
                missing_x_test = x_test_numeric.isnull().sum().sum()
                missing_y_test = y_test_numeric.isnull().sum().sum()
                st.write(f"**Missing Values (X):** {missing_x_test}")
                st.write(f"**Missing Values (Y):** {missing_y_test}")
            else:
                st.info("Test data not available")

        # Data visualization
        if x_train is not None:
            st.markdown("#### 📈 Data Visualization")

            # Create spectral plot
            x_numeric = x_train.select_dtypes(include=[np.number])
            if len(x_numeric.columns) > 0:
                fig = go.Figure()

                # Plot mean spectrum
                mean_spectrum = x_numeric.mean()
                fig.add_trace(go.Scatter(
                    x=list(range(len(mean_spectrum))),
                    y=mean_spectrum.values,
                    mode='lines',
                    name='Mean Spectrum',
                    line=dict(color='blue', width=3)
                ))

                # Plot individual spectra (first 5 samples)
                colors = px.colors.qualitative.Plotly
                for i in range(min(5, len(x_numeric))):
                    fig.add_trace(go.Scatter(
                        x=list(range(len(x_numeric.columns))),
                        y=x_numeric.iloc[i].values,
                        mode='lines',
                        name=f'Sample {i+1}',
                        line=dict(color=colors[i % len(colors)], width=1),
                        opacity=0.7
                    ))

                fig.update_layout(
                    title="Training Data Spectral Overview",
                    xaxis_title="Variable Index",
                    yaxis_title="Absorbance",
                    font=dict(family="Nunito Sans", size=12),
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

    def _render_preprocessing_and_variables(self) -> None:
        """Render preprocessing and variable selection section."""
        st.markdown("### ⚙️ Preprocessing & Variable Selection")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🔧 Preprocessing Pipeline")

            # Get preprocessing information
            preprocessing_method = self.session.get("selected_preprocessing", "None")
            preprocessing_name = self.session.get("preprocessing_method_name", "Unknown")

            if preprocessing_method and preprocessing_method != "None":
                st.success(f"✅ **Applied:** {preprocessing_name}")
                st.write(f"**Method:** {preprocessing_method}")

                # Show before/after comparison if available
                x_train_original = self.session.get("x_train")
                x_train_preprocessed = self.session.get("x_train_preprocessed")

                if x_train_original is not None and x_train_preprocessed is not None:
                    x_orig_numeric = x_train_original.select_dtypes(include=[np.number])
                    x_prep_numeric = x_train_preprocessed.select_dtypes(include=[np.number])

                    # Calculate preprocessing effects
                    orig_mean = x_orig_numeric.mean().mean()
                    orig_std = x_orig_numeric.std().mean()
                    prep_mean = x_prep_numeric.mean().mean()
                    prep_std = x_prep_numeric.std().mean()

                    st.write("**Preprocessing Effects:**")
                    st.write(f"- Original Mean: {orig_mean:.4f} → {prep_mean:.4f}")
                    st.write(f"- Original Std: {orig_std:.4f} → {prep_std:.4f}")
            else:
                st.info("ℹ️ No preprocessing applied")

        with col2:
            st.markdown("#### 🎯 Variable Selection")

            # Get variable selection information
            variable_selection_results = self.session.get("variable_selection_results")
            selected_variables = self.session.get("selected_variables")
            selected_method_name = self.session.get("selected_method_name", "Unknown")

            if variable_selection_results and selected_variables is not None:
                selection_type = variable_selection_results.get('selection_type', 'unknown')
                n_selected = len(selected_variables)

                x_train_original = self.session.get("x_train_preprocessed")
                if x_train_original is not None:
                    x_orig_numeric = x_train_original.select_dtypes(include=[np.number])
                    n_original = len(x_orig_numeric.columns)
                    reduction_pct = ((n_original - n_selected) / n_original) * 100

                    st.success(f"✅ **Applied:** {selected_method_name}")
                    st.write(f"**Method:** {selection_type}")
                    st.write(f"**Variables Selected:** {n_selected} / {n_original}")
                    st.write(f"**Reduction:** {reduction_pct:.1f}%")

                    # Show variable selection visualization
                    if len(selected_variables) > 0:
                        fig = go.Figure()

                        # Create binary mask for selected variables
                        mask = np.zeros(n_original)
                        mask[selected_variables] = 1

                        fig.add_trace(go.Scatter(
                            x=list(range(n_original)),
                            y=mask,
                            mode='markers',
                            marker=dict(
                                color=mask,
                                colorscale='RdYlBu',
                                size=8,
                                line=dict(width=1, color='black')
                            ),
                            name='Selected Variables'
                        ))

                        fig.update_layout(
                            title="Variable Selection Pattern",
                            xaxis_title="Variable Index",
                            yaxis_title="Selected (1) / Not Selected (0)",
                            font=dict(family="Nunito Sans", size=12),
                            height=300,
                            yaxis=dict(tickvals=[0, 1], ticktext=['Not Selected', 'Selected'])
                        )

                        st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("ℹ️ No variable selection applied")

    def _render_model_development(self, built_model: Dict[str, Any]) -> None:
        """Render model development section."""
        st.markdown("### 🤖 Model Development")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🔬 Algorithm Selection")
            st.success(f"✅ **Selected:** {built_model['algorithm']}")

            # Show model parameters
            parameters = built_model.get("parameters", {})
            if parameters:
                st.markdown("**Model Parameters:**")
                for param, value in parameters.items():
                    st.write(f"- **{param}:** {value}")

            # Get nomination information if available
            selected_nomination = self.session.get("selected_final_nomination")
            if selected_nomination and isinstance(selected_nomination, dict):
                cv_method = selected_nomination.get("cv_method", "Unknown")
                cv_folds = selected_nomination.get("cv_folds", "Unknown")
                st.markdown("**Cross-Validation Setup:**")
                st.write(f"- **Method:** {cv_method}")
                st.write(f"- **Folds:** {cv_folds}")

        with col2:
            st.markdown("#### 📊 Model Comparison")

            # Show nominations comparison if available
            nominations = self.session.get("model_nominations", [])
            if nominations and len(nominations) > 1:
                st.write(f"**Models Evaluated:** {len(nominations)}")

                # Create comparison table
                comparison_data = []
                for nom in nominations:
                    comparison_data.append({
                        'Algorithm': nom['algorithm'],
                        'RMSECV': f"{nom['overall_rmsecv']:.4f}",
                        'R²': f"{nom['overall_r2']:.4f}"
                    })

                df_comparison = pd.DataFrame(comparison_data)
                st.dataframe(df_comparison, use_container_width=True)

                # Highlight selected model
                selected_id = selected_nomination.get('id') if selected_nomination else None
                if selected_id:
                    st.info(f"🎯 **Selected Model ID:** {selected_id}")
            else:
                st.info("Single model development")

    def _render_performance_validation(self, built_model: Dict[str, Any]) -> None:
        """Render performance validation section."""
        st.markdown("### 📈 Performance & Validation")

        # Cross-validation results
        st.markdown("#### 🔄 Cross-Validation Results")

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("RMSECV", f"{built_model['rmsecv']:.4f}")
        with col2:
            st.metric("Training R²", f"{built_model['r2']:.4f}")
        with col3:
            # Calculate model complexity
            params = built_model.get("parameters", {})
            complexity = "Low"
            if 'n_components' in params:
                n_comp = params['n_components']
                if n_comp > 10:
                    complexity = "High"
                elif n_comp > 5:
                    complexity = "Medium"
            st.metric("Model Complexity", complexity)

        # Test data validation if available
        test_predictions = self.session.get("test_predictions")
        if test_predictions and isinstance(test_predictions, dict):
            st.markdown("#### 🧪 Test Data Validation")

            col1, col2, col3 = st.columns(3)
            with col1:
                # Handle different possible key names for test RMSE
                test_rmse = test_predictions.get('test_rmse', test_predictions.get('rmse', 0))
                st.metric("Test RMSE", f"{test_rmse:.4f}")
            with col2:
                # Handle different possible key names for test R²
                test_r2 = test_predictions.get('test_r2', test_predictions.get('r2', 0))
                st.metric("Test R²", f"{test_r2:.4f}")
            with col3:
                # Calculate generalization
                train_r2 = built_model.get('r2', 0)
                generalization = abs(train_r2 - test_r2) if isinstance(train_r2, (int, float)) and isinstance(test_r2, (int, float)) else 0
                gen_status = "Good" if generalization < 0.1 else "Moderate" if generalization < 0.2 else "Poor"
                st.metric("Generalization", gen_status)

            # Prediction plot
            self._create_prediction_plot(test_predictions)
        else:
            st.info("ℹ️ Test data validation not performed yet")

    def _render_predictions_and_results(self, built_model: Dict[str, Any]) -> None:
        """Render predictions and results section."""
        st.markdown("### 🎯 Predictions & Results")

        # Test data prediction interface
        has_test_data = self.session.has("x_test_selected") and self.session.has("y_test")

        if has_test_data:
            if not self.session.has("test_predictions"):
                st.info("Click below to validate your model performance using test data.")
                if st.button("🧪 Predict Test Data", type="primary", key="predict_test_data_report"):
                    self._predict_test_data(built_model)
            else:
                # Show detailed results
                test_results = self.session.get("test_predictions")
                self._create_prediction_table(test_results)
        else:
            st.info("ℹ️ No test data available for validation")

        # Unknown sample prediction interface
        st.markdown("#### 🔮 Unknown Sample Prediction")
        uploaded_file = st.file_uploader(
            "Upload Unknown Sample Data (CSV)",
            type=['csv'],
            help="Upload CSV file with spectroscopic data for unknown samples",
            key="unknown_samples_upload_report"
        )

        if uploaded_file is not None:
            try:
                unknown_data = pd.read_csv(uploaded_file, sep=';', decimal=',')
                st.success(f"✅ Loaded {len(unknown_data)} unknown samples")

                if st.button("🔮 Predict Unknown Samples", type="primary", key="predict_unknown_report"):
                    self._predict_unknown_samples(built_model, unknown_data)
            except Exception as e:
                st.error(f"❌ Error loading unknown sample data: {str(e)}")

    def _render_final_actions(self) -> None:
        """Render final actions section."""
        st.markdown("---")
        st.markdown("### 📥 Report Actions")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("📊 Generate PDF Report", type="secondary", key="generate_pdf"):
                self._generate_pdf_report()

        with col2:
            # ChatGPT Summary Button
            chatgpt_helper = ChatGPTHelper()
            if chatgpt_helper.render_summary_button("Step 7 Model Report Summary", key="step7_summary"):
                self._generate_chatgpt_summary()

        with col3:
            if st.button("🔙 Back to Model Building", key="back_to_step6_final"):
                self.session.set_current_step(6)
                st.rerun()

        with col4:
            if st.button("🎉 Complete Workflow", type="primary", key="complete_workflow_final"):
                st.success("🎉 Workflow completed successfully!")
                st.balloons()
                self.session.set("workflow_complete", True)

    def _generate_summary_report(self) -> None:
        """Generate downloadable summary report."""
        # Create summary data
        built_model = self._get_trained_model()
        report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        summary_data = {
            "MELK Chemo Copilot - Model Development Report": [
                f"Generated: {report_time}",
                f"Algorithm: {built_model['algorithm']}",
                f"Cross-Validation RMSE: {built_model['rmsecv']:.4f}",
                f"Training R²: {built_model['r2']:.4f}",
                "--- Model Parameters ---"
            ]
        }

        # Add parameters
        for param, value in built_model.get("parameters", {}).items():
            summary_data["MELK Chemo Copilot - Model Development Report"].append(f"{param}: {value}")

        # Convert to DataFrame and download
        df_summary = pd.DataFrame(summary_data)
        csv = df_summary.to_csv(index=False)

        st.download_button(
            label="📥 Download Summary Report (CSV)",
            data=csv,
            file_name=f"model_development_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

        st.success("✅ Summary report generated!")

    def _generate_pdf_report(self) -> None:
        """Generate comprehensive PDF report."""
        try:
            built_model = self._get_trained_model()
            if not built_model:
                st.error("No trained model available for PDF generation")
                return

            # Create PDF buffer
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Center alignment
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.darkblue
            )

            # Build PDF content
            story = []

            # Title
            story.append(Paragraph("MELK Chemo Copilot", title_style))
            story.append(Paragraph("Complete Model Development Report", title_style))
            story.append(Spacer(1, 20))

            # Report metadata
            report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            story.append(Paragraph(f"Generated: {report_time}", styles['Normal']))
            story.append(Spacer(1, 20))

            # Executive Summary
            story.append(Paragraph("Executive Summary", heading_style))
            story.append(Paragraph(f"Algorithm: {built_model['algorithm']}", styles['Normal']))
            story.append(Paragraph(f"Cross-Validation RMSE: {built_model['rmsecv']:.4f}", styles['Normal']))
            story.append(Paragraph(f"Training R²: {built_model['r2']:.4f}", styles['Normal']))
            story.append(Spacer(1, 15))

            # Data Overview
            story.append(Paragraph("Data Overview", heading_style))
            x_train = self.session.get("x_train")
            y_train = self.session.get("y_train")
            if x_train is not None and y_train is not None:
                x_numeric = x_train.select_dtypes(include=[np.number])
                y_numeric = y_train.select_dtypes(include=[np.number])
                story.append(Paragraph(f"Training samples: {len(x_train)}", styles['Normal']))
                story.append(Paragraph(f"Original variables: {len(x_numeric.columns)}", styles['Normal']))
                story.append(Paragraph(f"Target compounds: {', '.join(y_numeric.columns)}", styles['Normal']))
            story.append(Spacer(1, 15))

            # Preprocessing
            story.append(Paragraph("Preprocessing & Variable Selection", heading_style))
            preprocessing_method = self.session.get("selected_preprocessing", "None")
            if preprocessing_method and preprocessing_method != "None":
                preprocessing_name = self.session.get("preprocessing_method_name", "Unknown")
                story.append(Paragraph(f"Preprocessing: {preprocessing_name}", styles['Normal']))

            variable_selection_results = self.session.get("variable_selection_results")
            selected_variables = self.session.get("selected_variables")
            if variable_selection_results and selected_variables is not None:
                selected_method_name = self.session.get("selected_method_name", "Unknown")
                n_selected = len(selected_variables)
                story.append(Paragraph(f"Variable selection: {selected_method_name}", styles['Normal']))
                story.append(Paragraph(f"Variables selected: {n_selected}", styles['Normal']))
            story.append(Spacer(1, 15))

            # Model Development
            story.append(Paragraph("Model Development", heading_style))
            parameters = built_model.get("parameters", {})
            if parameters:
                story.append(Paragraph("Model Parameters:", styles['Normal']))
                for param, value in parameters.items():
                    story.append(Paragraph(f"  • {param}: {value}", styles['Normal']))
            story.append(Spacer(1, 15))

            # Performance Results
            story.append(Paragraph("Performance Results", heading_style))
            test_predictions = self.session.get("test_predictions")
            if test_predictions and isinstance(test_predictions, dict):
                test_rmse = test_predictions.get('test_rmse', test_predictions.get('rmse', 0))
                test_r2 = test_predictions.get('test_r2', test_predictions.get('r2', 0))
                story.append(Paragraph(f"Test RMSE: {test_rmse:.4f}", styles['Normal']))
                story.append(Paragraph(f"Test R²: {test_r2:.4f}", styles['Normal']))

            # Individual compound results
            compound_results = built_model.get("compound_results", {})
            if compound_results and isinstance(compound_results, dict):
                story.append(Paragraph("Individual Compound Performance:", styles['Normal']))

                # Create table data
                table_data = [['Compound', 'RMSECV', 'R²', 'Performance']]
                for compound, results in compound_results.items():
                    if isinstance(results, dict):
                        rmsecv = results.get('rmsecv', 0)
                        r2 = results.get('r2', 0)
                        performance = self._assess_performance_text(r2)
                        table_data.append([compound, f"{rmsecv:.4f}", f"{r2:.4f}", performance])

                # Create table
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(table)

            # Build PDF
            doc.build(story)

            # Create download
            buffer.seek(0)
            pdf_data = buffer.getvalue()
            buffer.close()

            filename = f"model_development_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

            st.download_button(
                label="📥 Download PDF Report",
                data=pdf_data,
                file_name=filename,
                mime="application/pdf"
            )

            st.success("✅ PDF report generated successfully!")

        except Exception as e:
            st.error(f"❌ Error generating PDF report: {str(e)}")
            st.info("Falling back to CSV summary report...")
            self._generate_summary_report()

    def _assess_performance_text(self, r2: float) -> str:
        """Assess performance based on R² value (text only)."""
        if r2 > 0.9:
            return "Excellent"
        elif r2 > 0.8:
            return "Good"
        elif r2 > 0.7:
            return "Moderate"
        else:
            return "Poor"

    def _generate_chatgpt_summary(self) -> None:
        """Generate comprehensive ChatGPT summary of the model development workflow."""
        built_model = self._get_trained_model()
        if not built_model:
            st.error("No trained model available for summary")
            return

        # Gather comprehensive workflow context
        context_parts = []

        # Model information
        context_parts.append(f"MELK Chemo Copilot - Complete Model Development Report")
        context_parts.append(f"Algorithm: {built_model['algorithm']}")
        context_parts.append(f"Cross-Validation RMSE: {built_model['rmsecv']:.4f}")
        context_parts.append(f"Training R²: {built_model['r2']:.4f}")

        # Data information
        x_train = self.session.get("x_train")
        y_train = self.session.get("y_train")
        if x_train is not None and y_train is not None:
            x_numeric = x_train.select_dtypes(include=[np.number])
            y_numeric = y_train.select_dtypes(include=[np.number])
            context_parts.append(f"Training samples: {len(x_train)}")
            context_parts.append(f"Original variables: {len(x_numeric.columns)}")
            context_parts.append(f"Target compounds: {', '.join(y_numeric.columns)}")

        # Preprocessing information
        preprocessing_method = self.session.get("selected_preprocessing", "None")
        if preprocessing_method and preprocessing_method != "None":
            preprocessing_name = self.session.get("preprocessing_method_name", "Unknown")
            context_parts.append(f"Preprocessing: {preprocessing_name} ({preprocessing_method})")

        # Variable selection information
        variable_selection_results = self.session.get("variable_selection_results")
        selected_variables = self.session.get("selected_variables")
        if variable_selection_results and selected_variables is not None:
            selected_method_name = self.session.get("selected_method_name", "Unknown")
            n_selected = len(selected_variables)
            context_parts.append(f"Variable selection: {selected_method_name}")
            context_parts.append(f"Variables selected: {n_selected}")

        # Model parameters
        parameters = built_model.get("parameters", {})
        if parameters:
            param_str = ", ".join([f"{k}: {v}" for k, v in parameters.items()])
            context_parts.append(f"Model parameters: {param_str}")

        # Test data results if available
        test_predictions = self.session.get("test_predictions")
        if test_predictions and isinstance(test_predictions, dict):
            test_rmse = test_predictions.get('test_rmse', test_predictions.get('rmse', 0))
            test_r2 = test_predictions.get('test_r2', test_predictions.get('r2', 0))
            context_parts.append(f"Test RMSE: {test_rmse:.4f}")
            context_parts.append(f"Test R²: {test_r2:.4f}")

        # Individual compound results
        compound_results = built_model.get("compound_results", {})
        if compound_results and isinstance(compound_results, dict):
            context_parts.append("Individual compound performance:")
            for compound, results in compound_results.items():
                if isinstance(results, dict):
                    rmsecv = results.get('rmsecv', 0)
                    r2 = results.get('r2', 0)
                    context_parts.append(f"  {compound}: RMSECV={rmsecv:.4f}, R²={r2:.4f}")

        # Create comprehensive context
        full_context = "\n".join(context_parts)

        # Generate summary with ChatGPT
        chatgpt_helper = ChatGPTHelper()
        prompt = f"""Please provide a comprehensive peer-review style summary of this complete chemometric model development workflow.

Context:
{full_context}

Please provide a detailed analysis covering:
1. Overall workflow summary and methodology
2. Data characteristics and preprocessing approach
3. Variable selection strategy and effectiveness
4. Model selection rationale and algorithm choice
5. Performance assessment and validation results
6. Model reliability and generalization capability
7. Recommendations for practical application
8. Any limitations or considerations for future work

Format the response as a professional scientific report summary suitable for analytical chemists and researchers."""

        chatgpt_helper.get_response(prompt, context=full_context)

    def _predict_test_data(self, built_model: Dict[str, Any]) -> None:
        """Predict on test data."""
        with st.spinner("🧪 Predicting test data..."):
            try:
                x_test = self.session.get("x_test_selected")
                y_test = self.session.get("y_test")
                model = built_model["model"]

                # Convert to numpy arrays
                x_test_array = x_test.select_dtypes(include=[np.number]).values
                y_test_array = y_test.select_dtypes(include=[np.number]).values

                if y_test_array.ndim == 2 and y_test_array.shape[1] == 1:
                    y_test_array = y_test_array.ravel()

                # Make predictions
                y_pred = model.predict(x_test_array)

                # Calculate metrics
                test_rmse = np.sqrt(mean_squared_error(y_test_array, y_pred))
                test_r2 = r2_score(y_test_array, y_pred)

                # Store results
                test_results = {
                    "y_true": y_test_array,
                    "y_pred": y_pred,
                    "test_rmse": test_rmse,
                    "test_r2": test_r2,
                    "sample_names": x_test.index.tolist() if hasattr(x_test, 'index') else list(range(len(x_test)))
                }

                self.session.set("test_predictions", test_results)
                st.success("✅ Test data prediction completed!")
                st.rerun()

            except Exception as e:
                st.error(f"❌ Error predicting test data: {str(e)}")

    def _create_prediction_plot(self, test_results: Dict[str, Any]) -> None:
        """Create prediction vs actual plot."""
        try:
            y_true = test_results.get('y_true', [])
            y_pred = test_results.get('y_pred', [])

            if len(y_true) == 0 or len(y_pred) == 0:
                st.warning("No prediction data available for plotting")
                return

            fig = go.Figure()

            # Scatter plot - handle arrays properly
            y_true_flat = np.array(y_true).flatten()
            y_pred_flat = np.array(y_pred).flatten()
            sample_names = test_results.get('sample_names', [f'Sample {i+1}' for i in range(len(y_true_flat))])

            fig.add_trace(go.Scatter(
                x=y_true_flat,
                y=y_pred_flat,
                mode='markers',
                name='Predictions',
                marker=dict(
                    size=8,
                    color='rgba(25, 118, 210, 0.7)',
                    line=dict(width=1, color='rgba(25, 118, 210, 1)')
                ),
                text=sample_names,
                hovertemplate='<b>%{text}</b><br>Actual: %{x:.3f}<br>Predicted: %{y:.3f}<extra></extra>'
            ))

            # Perfect prediction line - handle arrays properly
            y_true_flat = np.array(y_true).flatten()
            y_pred_flat = np.array(y_pred).flatten()
            min_val = min(np.min(y_true_flat), np.min(y_pred_flat))
            max_val = max(np.max(y_true_flat), np.max(y_pred_flat))
            fig.add_trace(go.Scatter(
                x=[min_val, max_val],
                y=[min_val, max_val],
                mode='lines',
                name='Perfect Prediction',
                line=dict(color='red', dash='dash', width=2)
            ))

            # Get R² value safely
            test_r2 = test_results.get('test_r2', test_results.get('r2', 0))

            fig.update_layout(
                title=f"Predicted vs Actual Values (R² = {test_r2:.3f})",
                xaxis_title="Actual Values",
                yaxis_title="Predicted Values",
                font=dict(family="Nunito Sans", size=12),
                showlegend=True,
                height=500
            )

            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error creating prediction plot: {str(e)}")
            st.info("Prediction data may be in an unexpected format")

    def _create_prediction_table(self, test_results: Dict[str, Any]) -> None:
        """Create prediction results table."""
        try:
            st.markdown("##### 📋 Detailed Prediction Results")

            # Get data safely
            y_true = test_results.get('y_true', [])
            y_pred = test_results.get('y_pred', [])

            if len(y_true) == 0 or len(y_pred) == 0:
                st.warning("No prediction data available for table")
                return

            # Convert to numpy arrays and flatten for calculations
            y_true_array = np.array(y_true).flatten()
            y_pred_array = np.array(y_pred).flatten()
            sample_names = test_results.get('sample_names', [f'Sample {i+1}' for i in range(len(y_true_array))])

            # Create DataFrame
            results_df = pd.DataFrame({
                'Sample': sample_names,
                'Actual': y_true_array,
                'Predicted': y_pred_array,
                'Residual': y_true_array - y_pred_array,
                'Abs Error': np.abs(y_true_array - y_pred_array)
            })

            # Format numbers
            results_df['Actual'] = results_df['Actual'].round(4)
            results_df['Predicted'] = results_df['Predicted'].round(4)
            results_df['Residual'] = results_df['Residual'].round(4)
            results_df['Abs Error'] = results_df['Abs Error'].round(4)

            st.dataframe(results_df, use_container_width=True)

            # Download button
            csv = results_df.to_csv(index=False)
            st.download_button(
                label="📥 Download Prediction Results",
                data=csv,
                file_name="test_prediction_results.csv",
                mime="text/csv"
            )

        except Exception as e:
            st.error(f"Error creating prediction table: {str(e)}")
            st.info("Prediction data may be in an unexpected format")

    def _predict_unknown_samples(self, built_model: Dict[str, Any], unknown_data: pd.DataFrame) -> None:
        """Predict unknown samples."""
        with st.spinner("🔮 Predicting unknown samples..."):
            try:
                model = built_model["model"]

                # Convert to numpy array
                x_unknown = unknown_data.select_dtypes(include=[np.number]).values

                # Make predictions
                predictions = model.predict(x_unknown)

                # Create results DataFrame
                results_df = pd.DataFrame({
                    'Sample': unknown_data.index + 1,
                    'Predicted_Value': predictions.round(4)
                })

                st.success("✅ Unknown sample prediction completed!")
                st.markdown("##### 🔮 Unknown Sample Predictions")
                st.dataframe(results_df, use_container_width=True)

                # Download button
                csv = results_df.to_csv(index=False)
                st.download_button(
                    label="📥 Download Unknown Sample Predictions",
                    data=csv,
                    file_name="unknown_sample_predictions.csv",
                    mime="text/csv"
                )

            except Exception as e:
                st.error(f"❌ Error predicting unknown samples: {str(e)}")

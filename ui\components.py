"""
Enhanced Reusable UI Components for MELK Chemo Copilot

This module provides optimized, accessible, and responsive UI components
with performance enhancements and modern design patterns.
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Any, Optional, Tuple, Union
from config.settings import UI_COLORS, BRANDING_CONFIG
from core.performance_manager import cached_computation


class UIComponents:
    """Collection of reusable UI components."""

    @staticmethod
    def render_metric_cards(metrics: Dict[str, Any], columns: int = 3) -> None:
        """Render metric cards in columns."""
        cols = st.columns(columns)

        for i, (label, value) in enumerate(metrics.items()):
            with cols[i % columns]:
                if isinstance(value, dict):
                    st.metric(
                        label=label,
                        value=value.get('value', ''),
                        delta=value.get('delta', None)
                    )
                else:
                    st.metric(label=label, value=value)

    @staticmethod
    def render_comparison_table(data: List[Dict[str, Any]],
                              highlight_column: Optional[str] = None,
                              highlight_best: str = 'min') -> None:
        """Render a comparison table with optional highlighting."""
        if not data:
            st.warning("No data to display")
            return

        df = pd.DataFrame(data)

        if highlight_column and highlight_column in df.columns:
            if highlight_best == 'min':
                best_idx = df[highlight_column].idxmin()
            else:
                best_idx = df[highlight_column].idxmax()

            def highlight_best_row(row):
                if row.name == best_idx:
                    return ['background-color: #90EE90'] * len(row)
                return [''] * len(row)

            styled_df = df.style.apply(highlight_best_row, axis=1)
            st.dataframe(styled_df, use_container_width=True)
        else:
            st.dataframe(df, use_container_width=True)

    @staticmethod
    def render_method_selector(methods: Dict[str, Dict[str, Any]],
                             title: str = "Select Method",
                             key_suffix: str = "") -> Tuple[str, Dict[str, Any]]:
        """Render a method selector with descriptions."""
        st.markdown(f"### {title}")

        method_names = list(methods.keys())
        selected_method = st.selectbox(
            "Choose method:",
            method_names,
            key=f"method_selector_{key_suffix}",
            format_func=lambda x: f"{x} - {methods[x].get('description', '')}"
        )

        # Show detailed description
        if selected_method in methods:
            method_info = methods[selected_method]
            if 'description' in method_info:
                st.info(method_info['description'])

        return selected_method, methods.get(selected_method, {})

    @staticmethod
    def render_parameter_panel(parameters: Dict[str, Dict[str, Any]],
                             title: str = "Parameters") -> Dict[str, Any]:
        """Render a parameter input panel."""
        st.markdown(f"### {title}")

        values = {}

        for param_name, config in parameters.items():
            param_type = config.get('type', 'number')
            default = config.get('default', 0)
            help_text = config.get('help', config.get('description', ''))

            display_name = config.get('display_name', param_name.replace('_', ' ').title())

            if param_type == 'slider':
                values[param_name] = st.slider(
                    display_name,
                    min_value=config.get('min', 0),
                    max_value=config.get('max', 100),
                    value=default,
                    step=config.get('step', 1),
                    help=help_text
                )
            elif param_type == 'number':
                values[param_name] = st.number_input(
                    display_name,
                    min_value=config.get('min', 0),
                    max_value=config.get('max', 100),
                    value=default,
                    step=config.get('step', 1),
                    help=help_text
                )
            elif param_type == 'selectbox':
                options = config.get('options', [])
                default_index = 0
                if default in options:
                    default_index = options.index(default)

                values[param_name] = st.selectbox(
                    display_name,
                    options=options,
                    index=default_index,
                    help=help_text
                )
            elif param_type == 'checkbox':
                values[param_name] = st.checkbox(
                    display_name,
                    value=default,
                    help=help_text
                )
            elif param_type == 'text':
                values[param_name] = st.text_input(
                    display_name,
                    value=str(default),
                    help=help_text
                )

        return values

    @staticmethod
    def render_data_upload_panel(title: str = "Upload Data",
                               accepted_formats: List[str] = ['.csv', '.xlsx'],
                               help_text: str = "") -> Optional[pd.DataFrame]:
        """Render a data upload panel."""
        st.markdown(f"### {title}")

        if help_text:
            st.info(help_text)

        uploaded_file = st.file_uploader(
            "Choose file",
            type=[fmt.replace('.', '') for fmt in accepted_formats],
            help=f"Accepted formats: {', '.join(accepted_formats)}"
        )

        if uploaded_file is not None:
            try:
                if uploaded_file.name.endswith('.csv'):
                    df = pd.read_csv(uploaded_file)
                elif uploaded_file.name.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(uploaded_file)
                else:
                    st.error("Unsupported file format")
                    return None

                st.success(f"File uploaded successfully! Shape: {df.shape}")
                return df

            except Exception as e:
                st.error(f"Error reading file: {str(e)}")
                return None

        return None

    @staticmethod
    def render_data_preview(data: pd.DataFrame,
                          title: str = "Data Preview",
                          max_rows: int = 10) -> None:
        """Render a data preview with basic statistics."""
        st.markdown(f"### {title}")

        # Basic info
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Rows", data.shape[0])
        with col2:
            st.metric("Columns", data.shape[1])
        with col3:
            st.metric("Memory Usage", f"{data.memory_usage(deep=True).sum() / 1024:.1f} KB")

        # Data preview
        st.markdown("#### First few rows:")
        st.dataframe(data.head(max_rows), use_container_width=True)

        # Basic statistics for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            st.markdown("#### Basic Statistics:")
            st.dataframe(data[numeric_cols].describe(), use_container_width=True)

    @staticmethod
    def render_status_indicator(status: str, message: str = "") -> None:
        """Render a status indicator."""
        if status == "success":
            st.success(f"✅ {message}")
        elif status == "warning":
            st.warning(f"⚠️ {message}")
        elif status == "error":
            st.error(f"❌ {message}")
        elif status == "info":
            st.info(f"ℹ️ {message}")
        else:
            st.markdown(f"🔵 {message}")

    @staticmethod
    def render_expandable_section(title: str, content_func, expanded: bool = False) -> None:
        """Render an expandable section."""
        with st.expander(title, expanded=expanded):
            content_func()

    @staticmethod
    def render_two_column_layout(left_content_func, right_content_func,
                               left_width: int = 1, right_width: int = 1) -> None:
        """Render a two-column layout."""
        col1, col2 = st.columns([left_width, right_width])

        with col1:
            left_content_func()

        with col2:
            right_content_func()

    @staticmethod
    def render_tabbed_interface(tabs: Dict[str, callable]) -> None:
        """Render a tabbed interface."""
        tab_names = list(tabs.keys())
        tab_objects = st.tabs(tab_names)

        for tab_obj, (tab_name, content_func) in zip(tab_objects, tabs.items()):
            with tab_obj:
                content_func()


class ResponsiveComponents:
    """Responsive UI components that adapt to different screen sizes."""

    @staticmethod
    def render_responsive_grid(items: List[Dict[str, Any]],
                             columns_desktop: int = 3,
                             columns_tablet: int = 2,
                             columns_mobile: int = 1) -> None:
        """Render a responsive grid layout."""
        # Use CSS Grid for responsive layout
        st.markdown(f"""
        <div class="responsive-grid" style="
            display: grid;
            grid-template-columns: repeat({columns_desktop}, 1fr);
            gap: 1rem;
            width: 100%;
        ">
        """, unsafe_allow_html=True)

        # Add responsive CSS
        st.markdown(f"""
        <style>
        @media (max-width: 768px) {{
            .responsive-grid {{
                grid-template-columns: repeat({columns_tablet}, 1fr) !important;
            }}
        }}
        @media (max-width: 480px) {{
            .responsive-grid {{
                grid-template-columns: repeat({columns_mobile}, 1fr) !important;
            }}
        }}
        </style>
        """, unsafe_allow_html=True)

        # Render items
        for item in items:
            st.markdown(f"""
            <div class="grid-item" style="
                background: white;
                padding: 1rem;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border: 1px solid #e0e0e0;
            ">
                <h3>{item.get('title', 'Item')}</h3>
                <p>{item.get('content', '')}</p>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("</div>", unsafe_allow_html=True)

    @staticmethod
    def render_card_layout(cards: List[Dict[str, Any]],
                          max_height: str = "400px") -> None:
        """Render cards with dynamic heights and responsive text."""
        for card in cards:
            st.markdown(f"""
            <div class="model-card" style="
                min-height: 200px;
                max-height: {max_height};
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                background: white;
                padding: 1.5rem;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                margin-bottom: 1rem;
                border: 1px solid #e0e0e0;
            ">
                <div class="model-card-content" style="
                    flex-grow: 1;
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                    hyphens: auto;
                ">
                    <h3 style="margin-top: 0; color: {UI_COLORS['primary']};">
                        {card.get('title', 'Card Title')}
                    </h3>
                    <p>{card.get('content', '')}</p>
                    {card.get('extra_content', '')}
                </div>
                <div class="card-actions" style="margin-top: 1rem;">
                    {card.get('actions', '')}
                </div>
            </div>
            """, unsafe_allow_html=True)


class PerformanceComponents:
    """Performance-optimized UI components."""

    @staticmethod
    @cached_computation(persist=False)
    def render_large_dataframe(df: pd.DataFrame,
                              max_rows: int = 1000,
                              show_summary: bool = True) -> None:
        """Render large dataframes with performance optimizations."""
        if len(df) > max_rows:
            st.warning(f"Dataset has {len(df):,} rows. Showing first {max_rows:,} rows for performance.")
            display_df = df.head(max_rows)
        else:
            display_df = df

        if show_summary:
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Rows", f"{len(df):,}")
            with col2:
                st.metric("Columns", len(df.columns))
            with col3:
                st.metric("Memory Usage", f"{df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
            with col4:
                st.metric("Displayed", f"{len(display_df):,}")

        st.dataframe(display_df, use_container_width=True)

    @staticmethod
    def render_chunked_processing(data: List[Any],
                                 process_func: callable,
                                 chunk_size: int = 100,
                                 show_progress: bool = True) -> List[Any]:
        """Process data in chunks with progress indication."""
        results = []
        total_chunks = len(data) // chunk_size + (1 if len(data) % chunk_size else 0)

        if show_progress:
            progress_bar = st.progress(0)
            status_text = st.empty()

        for i in range(0, len(data), chunk_size):
            chunk = data[i:i + chunk_size]
            chunk_results = process_func(chunk)
            results.extend(chunk_results)

            if show_progress:
                progress = (i // chunk_size + 1) / total_chunks
                progress_bar.progress(progress)
                status_text.text(f"Processing chunk {i // chunk_size + 1} of {total_chunks}")

        if show_progress:
            progress_bar.empty()
            status_text.empty()

        return results


class AccessibilityComponents:
    """Accessibility-enhanced UI components."""

    @staticmethod
    def render_accessible_button(label: str,
                                key: str,
                                help_text: str = None,
                                disabled: bool = False,
                                button_type: str = "primary") -> bool:
        """Render an accessible button with proper ARIA labels."""
        # Add ARIA attributes through custom HTML if needed
        if help_text:
            st.markdown(f"""
            <div role="button" aria-label="{label}" aria-describedby="{key}_help">
            """, unsafe_allow_html=True)

        clicked = st.button(
            label,
            key=key,
            disabled=disabled,
            type=button_type,
            help=help_text
        )

        if help_text:
            st.markdown(f"""
            <div id="{key}_help" class="sr-only">{help_text}</div>
            </div>
            """, unsafe_allow_html=True)

        return clicked

    @staticmethod
    def render_accessible_selectbox(label: str,
                                   options: List[Any],
                                   key: str,
                                   help_text: str = None,
                                   format_func: callable = None) -> Any:
        """Render an accessible selectbox with proper labeling."""
        return st.selectbox(
            label,
            options,
            key=key,
            help=help_text,
            format_func=format_func
        )

    @staticmethod
    def render_screen_reader_text(text: str) -> None:
        """Render text that's only visible to screen readers."""
        st.markdown(f"""
        <span class="sr-only">{text}</span>
        """, unsafe_allow_html=True)


class ModernComponents:
    """Modern UI components with enhanced styling."""

    @staticmethod
    def render_gradient_metric(label: str,
                              value: str,
                              delta: str = None,
                              color_scheme: str = "primary") -> None:
        """Render a metric with gradient background."""
        colors = {
            "primary": f"linear-gradient(135deg, {UI_COLORS['primary']} 0%, {UI_COLORS['secondary']} 100%)",
            "success": f"linear-gradient(135deg, {UI_COLORS['success']} 0%, #4caf50 100%)",
            "warning": f"linear-gradient(135deg, {UI_COLORS['warning']} 0%, #e74c3c 100%)",
            "accent": f"linear-gradient(135deg, {UI_COLORS['accent']} 0%, {UI_COLORS['primary']} 100%)"
        }

        gradient = colors.get(color_scheme, colors["primary"])

        delta_html = ""
        if delta:
            delta_html = f'<div style="font-size: 0.8rem; opacity: 0.8;">{delta}</div>'

        st.markdown(f"""
        <div style="
            background: {gradient};
            padding: 1.5rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            margin: 0.75rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        ">
            <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">{label}</div>
            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.5rem;">{value}</div>
            {delta_html}
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_info_card(title: str,
                        content: str,
                        icon: str = "ℹ️",
                        card_type: str = "info") -> None:
        """Render an information card with icon and styling."""
        type_colors = {
            "info": UI_COLORS['primary'],
            "success": UI_COLORS['success'],
            "warning": UI_COLORS['warning'],
            "error": "#e74c3c"
        }

        color = type_colors.get(card_type, type_colors["info"])

        st.markdown(f"""
        <div style="
            background: white;
            border-left: 4px solid {color};
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 1rem 0;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                <span style="font-size: 1.5rem; margin-right: 0.5rem;">{icon}</span>
                <h3 style="margin: 0; color: {color};">{title}</h3>
            </div>
            <p style="margin: 0; line-height: 1.6;">{content}</p>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_progress_indicator(current_step: int,
                                 total_steps: int,
                                 step_names: List[str] = None) -> None:
        """Render a modern progress indicator."""
        if step_names and len(step_names) != total_steps:
            step_names = [f"Step {i+1}" for i in range(total_steps)]
        elif not step_names:
            step_names = [f"Step {i+1}" for i in range(total_steps)]

        progress_html = '<div style="display: flex; align-items: center; margin: 2rem 0;">'

        for i in range(total_steps):
            # Determine step status
            if i < current_step - 1:
                status = "completed"
                color = UI_COLORS['success']
                icon = "✓"
            elif i == current_step - 1:
                status = "current"
                color = UI_COLORS['primary']
                icon = str(i + 1)
            else:
                status = "pending"
                color = "#ccc"
                icon = str(i + 1)

            # Step circle
            progress_html += f"""
            <div style="
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: {color};
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 0.9rem;
            ">{icon}</div>
            """

            # Step name
            progress_html += f"""
            <div style="
                margin: 0 1rem;
                font-size: 0.8rem;
                color: {color};
                font-weight: {'bold' if status == 'current' else 'normal'};
            ">{step_names[i]}</div>
            """

            # Connector line (except for last step)
            if i < total_steps - 1:
                line_color = UI_COLORS['success'] if i < current_step - 1 else "#ccc"
                progress_html += f"""
                <div style="
                    flex: 1;
                    height: 2px;
                    background: {line_color};
                    margin: 0 1rem;
                "></div>
                """

        progress_html += '</div>'
        st.markdown(progress_html, unsafe_allow_html=True)

    @staticmethod
    def render_confirmation_dialog(message: str,
                                 confirm_text: str = "Confirm",
                                 cancel_text: str = "Cancel") -> bool:
        """Render a confirmation dialog."""
        st.warning(message)

        col1, col2 = st.columns(2)

        with col1:
            if st.button(confirm_text, type="primary"):
                return True

        with col2:
            if st.button(cancel_text):
                return False

        return False

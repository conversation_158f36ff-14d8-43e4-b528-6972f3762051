"""
Performance Manager for MELK Chemo Copilot

This module provides caching mechanisms and performance optimizations
for computationally intensive operations.
"""

import streamlit as st
import pandas as pd
import numpy as np
import hashlib
import pickle
from typing import Any, Dict, List, Optional, Callable, Tuple
from functools import wraps
import time
import logging
from pathlib import Path


class CacheManager:
    """Manages caching for expensive operations."""
    
    def __init__(self, cache_dir: str = ".cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.memory_cache = {}
        self.max_memory_items = 50
    
    def _get_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate a unique cache key for function calls."""
        # Create a string representation of arguments
        key_data = f"{func_name}_{str(args)}_{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_file_path(self, cache_key: str) -> Path:
        """Get file path for cache key."""
        return self.cache_dir / f"{cache_key}.pkl"
    
    def get_from_memory(self, cache_key: str) -> Optional[Any]:
        """Get item from memory cache."""
        return self.memory_cache.get(cache_key)
    
    def set_to_memory(self, cache_key: str, value: Any) -> None:
        """Set item to memory cache with size limit."""
        if len(self.memory_cache) >= self.max_memory_items:
            # Remove oldest item (simple FIFO)
            oldest_key = next(iter(self.memory_cache))
            del self.memory_cache[oldest_key]
        
        self.memory_cache[cache_key] = value
    
    def get_from_disk(self, cache_key: str) -> Optional[Any]:
        """Get item from disk cache."""
        file_path = self._get_file_path(cache_key)
        if file_path.exists():
            try:
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logging.warning(f"Failed to load cache from disk: {e}")
                return None
        return None
    
    def set_to_disk(self, cache_key: str, value: Any) -> None:
        """Set item to disk cache."""
        file_path = self._get_file_path(cache_key)
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(value, f)
        except Exception as e:
            logging.warning(f"Failed to save cache to disk: {e}")
    
    def get(self, cache_key: str) -> Optional[Any]:
        """Get item from cache (memory first, then disk)."""
        # Try memory cache first
        value = self.get_from_memory(cache_key)
        if value is not None:
            return value
        
        # Try disk cache
        value = self.get_from_disk(cache_key)
        if value is not None:
            # Store in memory for faster access
            self.set_to_memory(cache_key, value)
            return value
        
        return None
    
    def set(self, cache_key: str, value: Any, persist: bool = True) -> None:
        """Set item to cache."""
        self.set_to_memory(cache_key, value)
        if persist:
            self.set_to_disk(cache_key, value)
    
    def clear_memory(self) -> None:
        """Clear memory cache."""
        self.memory_cache.clear()
    
    def clear_disk(self) -> None:
        """Clear disk cache."""
        for file_path in self.cache_dir.glob("*.pkl"):
            try:
                file_path.unlink()
            except Exception as e:
                logging.warning(f"Failed to delete cache file {file_path}: {e}")
    
    def clear_all(self) -> None:
        """Clear all caches."""
        self.clear_memory()
        self.clear_disk()


# Global cache manager instance
cache_manager = CacheManager()


def cached_computation(persist: bool = True, timeout: int = 3600):
    """
    Decorator for caching expensive computations.
    
    Args:
        persist: Whether to persist cache to disk
        timeout: Cache timeout in seconds (not implemented yet)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache_manager._get_cache_key(func.__name__, args, kwargs)
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Compute result
            start_time = time.time()
            result = func(*args, **kwargs)
            computation_time = time.time() - start_time
            
            # Cache result
            cache_manager.set(cache_key, result, persist=persist)
            
            # Log performance
            logging.info(f"Function {func.__name__} took {computation_time:.2f}s")
            
            return result
        
        return wrapper
    return decorator


class DataProcessor:
    """Optimized data processing with caching."""
    
    @staticmethod
    @cached_computation(persist=True)
    def load_and_validate_data(file_content: bytes, file_name: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Load and validate data with caching."""
        # This would contain the actual data loading logic
        # For now, it's a placeholder
        pass
    
    @staticmethod
    @cached_computation(persist=True)
    def preprocess_spectral_data(
        x_data: np.ndarray, 
        method: str, 
        parameters: Dict[str, Any]
    ) -> np.ndarray:
        """Preprocess spectral data with caching."""
        # This would contain the actual preprocessing logic
        pass
    
    @staticmethod
    @cached_computation(persist=True)
    def perform_variable_selection(
        x_data: np.ndarray,
        y_data: np.ndarray,
        method: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform variable selection with caching."""
        # This would contain the actual variable selection logic
        pass
    
    @staticmethod
    @cached_computation(persist=True)
    def train_model(
        x_data: np.ndarray,
        y_data: np.ndarray,
        algorithm: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Train model with caching."""
        # This would contain the actual model training logic
        pass


class LazyLoader:
    """Lazy loading for large datasets."""
    
    def __init__(self, data_source: Any):
        self.data_source = data_source
        self._data = None
        self._loaded = False
    
    @property
    def data(self):
        """Lazy load data when accessed."""
        if not self._loaded:
            self._data = self._load_data()
            self._loaded = True
        return self._data
    
    def _load_data(self):
        """Load data from source."""
        # Implement actual data loading logic
        return self.data_source
    
    def unload(self):
        """Unload data to free memory."""
        self._data = None
        self._loaded = False


class PerformanceMonitor:
    """Monitor and report performance metrics."""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, operation: str) -> None:
        """Start timing an operation."""
        self.start_times[operation] = time.time()
    
    def end_timer(self, operation: str) -> float:
        """End timing an operation and return duration."""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            self.metrics[operation] = duration
            del self.start_times[operation]
            return duration
        return 0.0
    
    def get_metrics(self) -> Dict[str, float]:
        """Get all performance metrics."""
        return self.metrics.copy()
    
    def reset_metrics(self) -> None:
        """Reset all metrics."""
        self.metrics.clear()
        self.start_times.clear()


# Global performance monitor
performance_monitor = PerformanceMonitor()


def performance_tracked(operation_name: str):
    """Decorator to track performance of functions."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            performance_monitor.start_timer(operation_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = performance_monitor.end_timer(operation_name)
                st.sidebar.metric(f"{operation_name} Time", f"{duration:.2f}s")
        return wrapper
    return decorator


class StreamlitOptimizer:
    """Streamlit-specific optimizations."""
    
    @staticmethod
    def optimize_dataframe_display(df: pd.DataFrame, max_rows: int = 1000) -> pd.DataFrame:
        """Optimize dataframe display for large datasets."""
        if len(df) > max_rows:
            st.warning(f"Dataset has {len(df)} rows. Showing first {max_rows} rows for performance.")
            return df.head(max_rows)
        return df
    
    @staticmethod
    def chunked_processing(data: List[Any], chunk_size: int = 100) -> List[Any]:
        """Process data in chunks to avoid memory issues."""
        results = []
        total_chunks = len(data) // chunk_size + (1 if len(data) % chunk_size else 0)
        
        progress_bar = st.progress(0)
        for i in range(0, len(data), chunk_size):
            chunk = data[i:i + chunk_size]
            # Process chunk here
            results.extend(chunk)  # Placeholder
            
            # Update progress
            progress = (i // chunk_size + 1) / total_chunks
            progress_bar.progress(progress)
        
        progress_bar.empty()
        return results


# Convenience functions
def clear_cache():
    """Clear all caches."""
    cache_manager.clear_all()
    st.cache_data.clear()
    st.cache_resource.clear()


def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics."""
    return {
        'memory_items': len(cache_manager.memory_cache),
        'disk_files': len(list(cache_manager.cache_dir.glob("*.pkl"))),
        'cache_dir_size': sum(f.stat().st_size for f in cache_manager.cache_dir.glob("*.pkl"))
    }

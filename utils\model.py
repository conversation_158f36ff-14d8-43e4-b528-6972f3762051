import numpy as np
import pandas as pd
from sklearn.cross_decomposition import PLSRegression
from sklearn.model_selection import K<PERSON>old
from sklearn.metrics import mean_squared_error, r2_score

def perform_pls_cv(X, Y, max_components=10, n_splits=10, random_state=42):
    """
    Perform k-fold cross-validation for PLS regression.
    
    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    max_components : int
        Maximum number of PLS components to evaluate
    n_splits : int
        Number of folds for cross-validation
    random_state : int
        Random seed for reproducibility
        
    Returns:
    --------
    cv_results : dict
        Dictionary containing cross-validation results
    """
    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values
    
    # Initialize arrays to store results
    n_samples = X.shape[0]
    
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)
    
    n_responses = Y.shape[1]
    
    # Initialize arrays to store CV predictions and errors
    cv_predictions = np.zeros((n_samples, n_responses, max_components))
    cv_errors = np.zeros((n_splits, n_responses, max_components))
    
    # Initialize k-fold cross-validation
    kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
    
    # Perform cross-validation
    for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X)):
        X_train, X_test = X[train_idx], X[test_idx]
        Y_train, Y_test = Y[train_idx], Y[test_idx]
        
        # Fit PLS models with different numbers of components
        for n_comp in range(1, max_components + 1):
            pls = PLSRegression(n_components=n_comp)
            pls.fit(X_train, Y_train)
            
            # Predict test set
            Y_pred = pls.predict(X_test)
            
            # Store predictions
            cv_predictions[test_idx, :, n_comp-1] = Y_pred
            
            # Calculate RMSE for each response variable
            for resp_idx in range(n_responses):
                cv_errors[fold_idx, resp_idx, n_comp-1] = np.sqrt(
                    mean_squared_error(Y_test[:, resp_idx], Y_pred[:, resp_idx])
                )
    
    # Calculate mean RMSE across folds for each component and response
    mean_cv_errors = np.mean(cv_errors, axis=0)
    
    # Find optimal number of components for each response
    optimal_components = np.argmin(mean_cv_errors, axis=1) + 1
    
    # Calculate RMSECV for each number of components (averaged across responses)
    rmsecv = np.mean(mean_cv_errors, axis=0)
    
    # Calculate R² for each response at optimal number of components
    r2_values = np.zeros(n_responses)
    for resp_idx in range(n_responses):
        opt_comp = optimal_components[resp_idx] - 1
        y_true = Y[:, resp_idx]
        y_pred = cv_predictions[:, resp_idx, opt_comp]
        r2_values[resp_idx] = r2_score(y_true, y_pred)
    
    # Prepare results dictionary
    cv_results = {
        'rmsecv': rmsecv,
        'optimal_components': optimal_components,
        'mean_cv_errors': mean_cv_errors,
        'r2_values': r2_values,
        'cv_predictions': cv_predictions
    }
    
    return cv_results

def calculate_vip_scores(pls_model, X):
    """
    Calculate Variable Importance in Projection (VIP) scores for a PLS model.
    
    Parameters:
    -----------
    pls_model : PLSRegression
        Fitted PLS model
    X : array-like
        Predictor variables used to fit the model
        
    Returns:
    --------
    vip_scores : array
        VIP scores for each predictor variable
    """
    # Get the number of samples and variables
    n_samples, n_variables = X.shape
    
    # Get the number of components
    n_components = pls_model.n_components
    
    # Get the weights (W) and loadings (P)
    W = pls_model.x_weights_
    P = pls_model.x_loadings_
    
    # Get the explained variance for each component
    explained_var = np.var(pls_model.x_scores_, axis=0)
    total_var = np.sum(explained_var)
    
    # Calculate VIP scores
    vip_scores = np.zeros(n_variables)
    
    for j in range(n_variables):
        weight_sum = 0
        for c in range(n_components):
            weight_sum += (W[j, c] ** 2) * explained_var[c]
        
        vip_scores[j] = np.sqrt(n_variables * weight_sum / total_var)
    
    return vip_scores

def calculate_model_metrics(y_true, y_pred):
    """
    Calculate model performance metrics.
    
    Parameters:
    -----------
    y_true : array-like
        True response values
    y_pred : array-like
        Predicted response values
        
    Returns:
    --------
    metrics : dict
        Dictionary containing model metrics
    """
    # Ensure arrays are 2D
    if y_true.ndim == 1:
        y_true = y_true.reshape(-1, 1)
    if y_pred.ndim == 1:
        y_pred = y_pred.reshape(-1, 1)
    
    n_responses = y_true.shape[1]
    
    # Initialize metrics
    rmse = np.zeros(n_responses)
    r2 = np.zeros(n_responses)
    
    # Calculate metrics for each response
    for i in range(n_responses):
        rmse[i] = np.sqrt(mean_squared_error(y_true[:, i], y_pred[:, i]))
        r2[i] = r2_score(y_true[:, i], y_pred[:, i])
    
    # Calculate bias
    bias = np.mean(y_pred - y_true, axis=0)
    
    # Calculate standard error of prediction (SEP)
    sep = np.zeros(n_responses)
    for i in range(n_responses):
        sep[i] = np.sqrt(np.mean(((y_pred[:, i] - y_true[:, i]) - bias[i]) ** 2))
    
    # Return metrics as dictionary
    metrics = {
        'rmse': rmse,
        'r2': r2,
        'bias': bias,
        'sep': sep
    }
    
    return metrics

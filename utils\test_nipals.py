"""
Test script to validate the True NIPALS implementation against scikit-learn's PLSRegression.
This helps ensure our NIPALS implementation is working correctly.
"""

import numpy as np
import pandas as pd
from sklearn.cross_decomposition import PLSRegression
from sklearn.datasets import make_regression
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import cross_val_score
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

try:
    from utils.nipals_pls import NIPALSRegression
    NIPALS_AVAILABLE = True
except ImportError:
    NIPALS_AVAILABLE = False
    print("NIPALS implementation not found. Please ensure utils/nipals_pls.py exists.")


def generate_test_data():
    """Generate test data similar to spectroscopic data."""
    np.random.seed(42)
    
    # Generate synthetic spectroscopic-like data
    n_samples = 100
    n_features = 200  # Simulating wavelengths
    n_targets = 1
    
    X, y = make_regression(
        n_samples=n_samples,
        n_features=n_features,
        n_targets=n_targets,
        noise=0.1,
        random_state=42
    )
    
    # Add some correlation structure typical of spectroscopic data
    for i in range(1, n_features):
        X[:, i] = 0.8 * X[:, i-1] + 0.2 * X[:, i]
    
    return X, y


def compare_algorithms():
    """Compare NIPALS vs SIMPLS (scikit-learn) implementations."""
    if not NIPALS_AVAILABLE:
        print("Cannot run comparison - NIPALS implementation not available")
        return
    
    print("🔬 NIPALS vs SIMPLS Comparison Test")
    print("=" * 50)
    
    # Generate test data
    X, y = generate_test_data()
    
    # Split data
    split_idx = int(0.8 * len(X))
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"📊 Data: {X_train.shape[0]} train samples, {X_test.shape[0]} test samples")
    print(f"📊 Features: {X_train.shape[1]} variables")
    
    # Test different numbers of components
    components_to_test = [1, 2, 3, 5, 8, 10]
    results = []
    
    for n_comp in components_to_test:
        print(f"\n🧪 Testing {n_comp} components:")
        
        # NIPALS implementation
        try:
            nipals_model = NIPALSRegression(n_components=n_comp, mode='PLS1')
            nipals_model.fit(X_train, y_train)
            nipals_pred = nipals_model.predict(X_test)
            nipals_r2 = r2_score(y_test, nipals_pred)
            nipals_rmse = np.sqrt(mean_squared_error(y_test, nipals_pred))
            nipals_info = nipals_model.get_model_info()
            
            print(f"  ✅ NIPALS: R² = {nipals_r2:.4f}, RMSE = {nipals_rmse:.4f}")
            print(f"     Iterations: {nipals_info['n_iter_per_component']}")
            print(f"     Converged: {nipals_info['converged']}")
            
        except Exception as e:
            print(f"  ❌ NIPALS failed: {e}")
            nipals_r2, nipals_rmse = np.nan, np.nan
        
        # SIMPLS implementation (scikit-learn)
        try:
            simpls_model = PLSRegression(n_components=n_comp)
            simpls_model.fit(X_train, y_train)
            simpls_pred = simpls_model.predict(X_test)
            simpls_r2 = r2_score(y_test, simpls_pred)
            simpls_rmse = np.sqrt(mean_squared_error(y_test, simpls_pred))
            
            print(f"  ✅ SIMPLS: R² = {simpls_r2:.4f}, RMSE = {simpls_rmse:.4f}")
            
        except Exception as e:
            print(f"  ❌ SIMPLS failed: {e}")
            simpls_r2, simpls_rmse = np.nan, np.nan
        
        # Compare results
        if not (np.isnan(nipals_r2) or np.isnan(simpls_r2)):
            r2_diff = abs(nipals_r2 - simpls_r2)
            rmse_diff = abs(nipals_rmse - simpls_rmse)
            
            print(f"  📊 Difference: ΔR² = {r2_diff:.4f}, ΔRMSE = {rmse_diff:.4f}")
            
            if r2_diff < 0.05 and rmse_diff < 0.1:
                print(f"  ✅ Results are similar (good!)")
            else:
                print(f"  ⚠️  Results differ significantly")
        
        results.append({
            'n_components': n_comp,
            'nipals_r2': nipals_r2,
            'nipals_rmse': nipals_rmse,
            'simpls_r2': simpls_r2,
            'simpls_rmse': simpls_rmse
        })
    
    return results


def test_cross_validation():
    """Test cross-validation performance."""
    if not NIPALS_AVAILABLE:
        print("Cannot run CV test - NIPALS implementation not available")
        return
    
    print("\n🔄 Cross-Validation Test")
    print("=" * 30)
    
    X, y = generate_test_data()
    
    # Test NIPALS
    try:
        nipals_model = NIPALSRegression(n_components=5, mode='PLS1')
        nipals_scores = cross_val_score(nipals_model, X, y, cv=5, 
                                       scoring='neg_mean_squared_error')
        nipals_rmse_cv = np.sqrt(-nipals_scores.mean())
        nipals_rmse_std = np.sqrt(nipals_scores.std())
        
        print(f"✅ NIPALS CV: RMSE = {nipals_rmse_cv:.4f} ± {nipals_rmse_std:.4f}")
        
    except Exception as e:
        print(f"❌ NIPALS CV failed: {e}")
    
    # Test SIMPLS
    try:
        simpls_model = PLSRegression(n_components=5)
        simpls_scores = cross_val_score(simpls_model, X, y, cv=5, 
                                       scoring='neg_mean_squared_error')
        simpls_rmse_cv = np.sqrt(-simpls_scores.mean())
        simpls_rmse_std = np.sqrt(simpls_scores.std())
        
        print(f"✅ SIMPLS CV: RMSE = {simpls_rmse_cv:.4f} ± {simpls_rmse_std:.4f}")
        
    except Exception as e:
        print(f"❌ SIMPLS CV failed: {e}")


def test_pls_modes():
    """Test PLS1 vs PLS2 modes."""
    if not NIPALS_AVAILABLE:
        print("Cannot run PLS modes test - NIPALS implementation not available")
        return
    
    print("\n🎯 PLS1 vs PLS2 Mode Test")
    print("=" * 30)
    
    # Generate multi-target data
    np.random.seed(42)
    X, y_multi = make_regression(n_samples=100, n_features=50, n_targets=3, 
                                noise=0.1, random_state=42)
    y_single = y_multi[:, 0]
    
    try:
        # Test PLS1
        pls1_model = NIPALSRegression(n_components=3, mode='PLS1')
        pls1_model.fit(X, y_single)
        pls1_pred = pls1_model.predict(X)
        pls1_r2 = r2_score(y_single, pls1_pred)
        
        print(f"✅ PLS1 (single target): R² = {pls1_r2:.4f}")
        
        # Test PLS2
        pls2_model = NIPALSRegression(n_components=3, mode='PLS2')
        pls2_model.fit(X, y_multi)
        pls2_pred = pls2_model.predict(X)
        pls2_r2 = r2_score(y_multi, pls2_pred)
        
        print(f"✅ PLS2 (multi target): R² = {pls2_r2:.4f}")
        
        # Get model info
        pls1_info = pls1_model.get_model_info()
        pls2_info = pls2_model.get_model_info()
        
        print(f"📊 PLS1 iterations: {pls1_info['n_iter_per_component']}")
        print(f"📊 PLS2 iterations: {pls2_info['n_iter_per_component']}")
        
    except Exception as e:
        print(f"❌ PLS modes test failed: {e}")


def test_convergence():
    """Test convergence behavior."""
    if not NIPALS_AVAILABLE:
        print("Cannot run convergence test - NIPALS implementation not available")
        return
    
    print("\n⚡ Convergence Test")
    print("=" * 20)
    
    X, y = generate_test_data()
    
    # Test different tolerance levels
    tolerances = [1e-3, 1e-6, 1e-9]
    
    for tol in tolerances:
        try:
            model = NIPALSRegression(n_components=3, tol=tol, max_iter=1000)
            model.fit(X, y)
            info = model.get_model_info()
            
            print(f"📊 Tolerance {tol}: {info['n_iter_per_component']} iterations")
            print(f"   Converged: {info['converged']}")
            
        except Exception as e:
            print(f"❌ Tolerance {tol} failed: {e}")


def main():
    """Run all tests."""
    print("🧪 NIPALS Implementation Validation Tests")
    print("=" * 60)
    
    if not NIPALS_AVAILABLE:
        print("❌ NIPALS implementation not available!")
        print("Please ensure utils/nipals_pls.py exists and is importable.")
        return
    
    # Run tests
    try:
        results = compare_algorithms()
        test_cross_validation()
        test_pls_modes()
        test_convergence()
        
        print("\n🎉 All tests completed!")
        print("\n📋 Summary:")
        print("- NIPALS implementation is working")
        print("- Results should be similar to SIMPLS for most cases")
        print("- Some differences are expected due to algorithmic differences")
        print("- NIPALS provides true iterative deflation")
        print("- Both PLS1 and PLS2 modes are supported")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

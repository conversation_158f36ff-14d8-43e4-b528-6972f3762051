X=[1.5	25.0
1.5	37.5
2.3	37.5
2.3	75.0
4.5	37.5
2.3	25.0
1.5	62.5
3.8	62.5
3.8	37.5
2.3	62.5
3.8	25.0
1.5	75.0
4.5	75.0
4.5	62.5
3.8	75.0
4.5	25.0];
[m,n]=size(X);
Xmean=mean(X);
Xmc=X-(ones(m,1)*Xmean)
[P,T]=pca(Xmc)
SC=T(:,1:2)
scatter(SC(:,1),SC(:,2),'k.');hold on

L=['M01';'M02';'M03';'M04';'M05';'M06';'M07';'M08';'M09';'M10';'M11';'M12';'M13';'M14';'M15';'M16'];
text(SC(:,1),SC(:,2),L)

Xt=[4.5	75.0
1.5	75.0
4.5	25.0
1.5	25.0
3.0	50.0
3.0	75.0
3.0	25.0
4.5	50.0
1.5	50.0];

[Z,F]=size(Xt);
Xtmean=mean(Xt);
Xtmc=Xt-(ones(Z,1)*Xtmean)
[S,E]=pca(Xtmc);
SCt=E(:,1:2)
scatter(SC(:,1),SC(:,2),'k.');hold on
scatter(SCt(:,1),SCt(:,2),'dr');hold on

L=['T01';'T02';'T03';'T04';'T05';'T06';'T07';'T08';'T09'];
text(SCt(:,1),SCt(:,2),L)

"""
Utility functions for parameter optimization and recommendations.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cross_decomposition import PLSRegression
from sklearn.model_selection import KFold
import itertools
from tqdm import tqdm
import streamlit as st

from utils.preprocessing import (
    mean_center_data,
    autoscale_data,
    apply_snv,
    apply_savgol,
    apply_snv_and_savgol
)

from utils.pls_algorithms import (
    create_pls_model,
    calculate_model_metrics
)

def optimize_preprocessing(X, y, n_components=None, pls_algorithm="Standard PLS", cv_folds=5):
    """
    Optimize preprocessing methods using cross-validation.
    
    Parameters:
    -----------
    X : array-like
        Input data
    y : array-like
        Target data
    n_components : int or list, optional
        Number of components to use. If None, will test a range of values.
    pls_algorithm : str
        PLS algorithm to use
    cv_folds : int
        Number of cross-validation folds
    
    Returns:
    --------
    results_df : pandas.DataFrame
        DataFrame with results for each preprocessing method
    best_method : str
        Best preprocessing method based on RMSECV
    """
    # Define preprocessing methods
    preprocessing_methods = {
        "Mean-centering": lambda x: mean_center_data(x),
        "Autoscaling": lambda x: autoscale_data(x),
        "SNV": lambda x: apply_snv(x),
        "Savitzky-Golay": lambda x: apply_savgol(x),
        "SNV + Savitzky-Golay": lambda x: apply_snv_and_savgol(x)
    }
    
    # If n_components is None, test a range of values
    if n_components is None:
        max_components = min(X.shape[0] - 2, X.shape[1], 20)
        n_components_list = list(range(1, max_components + 1))
    elif isinstance(n_components, int):
        n_components_list = [n_components]
    else:
        n_components_list = n_components
    
    # Initialize results
    results = []
    
    # Set up cross-validation
    kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    # Loop through preprocessing methods
    for method_name, preprocess_func in preprocessing_methods.items():
        # Preprocess data
        X_preprocessed = preprocess_func(X)
        
        # Loop through number of components
        for n_comp in n_components_list:
            # Initialize metrics for this combination
            rmse_cv_values = []
            r2_cv_values = []
            
            # Cross-validation
            for train_idx, test_idx in kf.split(X_preprocessed):
                X_train_fold, X_test_fold = X_preprocessed[train_idx], X_preprocessed[test_idx]
                y_train_fold, y_test_fold = y[train_idx], y[test_idx]
                
                # Create and fit model
                model = create_pls_model(n_comp, X_train_fold, y_train_fold, pls_algorithm=pls_algorithm)
                try:
                    model.fit(X_train_fold, y_train_fold)
                    y_pred = model.predict(X_test_fold)
                    
                    # Calculate metrics
                    metrics = calculate_model_metrics(y_test_fold, y_pred)
                    rmse_cv_values.append(np.mean(metrics['rmse']))
                    r2_cv_values.append(np.mean(metrics['r2']))
                except Exception as e:
                    # If model fitting fails, use high RMSE and low R²
                    rmse_cv_values.append(float('inf'))
                    r2_cv_values.append(0.0)
            
            # Calculate mean metrics
            mean_rmse_cv = np.mean(rmse_cv_values)
            mean_r2_cv = np.mean(r2_cv_values)
            
            # Add to results
            results.append({
                'Preprocessing Method': method_name,
                'Components': n_comp,
                'RMSECV': mean_rmse_cv,
                'R²': mean_r2_cv
            })
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Find best method and components
    best_idx = results_df['RMSECV'].idxmin()
    best_method = results_df.loc[best_idx, 'Preprocessing Method']
    best_components = results_df.loc[best_idx, 'Components']
    
    return results_df, best_method, best_components

def optimize_pls_algorithm(X, y, preprocessing_method="Mean-centering", n_components=None, cv_folds=5):
    """
    Optimize PLS algorithm using cross-validation.
    
    Parameters:
    -----------
    X : array-like
        Input data
    y : array-like
        Target data
    preprocessing_method : str
        Preprocessing method to use
    n_components : int or list, optional
        Number of components to use. If None, will test a range of values.
    cv_folds : int
        Number of cross-validation folds
    
    Returns:
    --------
    results_df : pandas.DataFrame
        DataFrame with results for each algorithm
    best_algorithm : str
        Best PLS algorithm based on RMSECV
    """
    # Define PLS algorithms
    pls_algorithms = ["Standard PLS", "NIPALS", "SIMPLS", "Kernel PLS", "OPLS"]
    
    # Preprocess data
    if preprocessing_method == "Mean-centering":
        X_preprocessed = mean_center_data(X)
    elif preprocessing_method == "Autoscaling":
        X_preprocessed = autoscale_data(X)
    elif preprocessing_method == "SNV":
        X_preprocessed = apply_snv(X)
    elif preprocessing_method == "Savitzky-Golay":
        X_preprocessed = apply_savgol(X)
    elif preprocessing_method == "SNV + Savitzky-Golay":
        X_preprocessed = apply_snv_and_savgol(X)
    else:
        X_preprocessed = X.copy()
    
    # If n_components is None, test a range of values
    if n_components is None:
        max_components = min(X_preprocessed.shape[0] - 2, X_preprocessed.shape[1], 20)
        n_components_list = list(range(1, max_components + 1))
    elif isinstance(n_components, int):
        n_components_list = [n_components]
    else:
        n_components_list = n_components
    
    # Initialize results
    results = []
    
    # Set up cross-validation
    kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    # Loop through algorithms
    for algorithm in pls_algorithms:
        # Loop through number of components
        for n_comp in n_components_list:
            # Initialize metrics for this combination
            rmse_cv_values = []
            r2_cv_values = []
            
            # Cross-validation
            for train_idx, test_idx in kf.split(X_preprocessed):
                X_train_fold, X_test_fold = X_preprocessed[train_idx], X_preprocessed[test_idx]
                y_train_fold, y_test_fold = y[train_idx], y[test_idx]
                
                # Special handling for OPLS with multiple Y variables
                if algorithm == "OPLS" and y_train_fold.shape[1] > 1:
                    # For OPLS with multiple Y variables, use only the first one
                    y_train_fold_opls = y_train_fold[:, 0].reshape(-1, 1)
                    
                    try:
                        model = create_pls_model(n_comp, X_train_fold, y_train_fold_opls, pls_algorithm=algorithm)
                        model.fit(X_train_fold, y_train_fold_opls)
                        
                        # For prediction, we'll need to handle the single Y case
                        y_pred_single = model.predict(X_test_fold)
                        
                        # Expand y_pred to match the original y shape
                        y_pred = np.zeros((y_test_fold.shape[0], y_test_fold.shape[1]))
                        y_pred[:, 0] = y_pred_single.ravel()
                    except Exception as e:
                        # If OPLS fails, use high RMSE and low R²
                        rmse_cv_values.append(float('inf'))
                        r2_cv_values.append(0.0)
                        continue
                else:
                    # For other algorithms or OPLS with single Y
                    try:
                        model = create_pls_model(n_comp, X_train_fold, y_train_fold, pls_algorithm=algorithm)
                        model.fit(X_train_fold, y_train_fold)
                        y_pred = model.predict(X_test_fold)
                    except Exception as e:
                        # If model fitting fails, use high RMSE and low R²
                        rmse_cv_values.append(float('inf'))
                        r2_cv_values.append(0.0)
                        continue
                
                # Calculate metrics
                metrics = calculate_model_metrics(y_test_fold, y_pred)
                rmse_cv_values.append(np.mean(metrics['rmse']))
                r2_cv_values.append(np.mean(metrics['r2']))
            
            # Calculate mean metrics
            if rmse_cv_values:
                mean_rmse_cv = np.mean(rmse_cv_values)
                mean_r2_cv = np.mean(r2_cv_values)
            else:
                mean_rmse_cv = float('inf')
                mean_r2_cv = 0.0
            
            # Add to results
            results.append({
                'Algorithm': algorithm,
                'Components': n_comp,
                'RMSECV': mean_rmse_cv,
                'R²': mean_r2_cv
            })
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Find best algorithm and components
    if not results_df.empty:
        best_idx = results_df['RMSECV'].idxmin()
        best_algorithm = results_df.loc[best_idx, 'Algorithm']
        best_components = results_df.loc[best_idx, 'Components']
    else:
        best_algorithm = "Standard PLS"
        best_components = 2
    
    return results_df, best_algorithm, best_components

def plot_optimization_results(results_df, x_col, y_col, hue_col, title):
    """
    Plot optimization results.
    
    Parameters:
    -----------
    results_df : pandas.DataFrame
        DataFrame with optimization results
    x_col : str
        Column to use for x-axis
    y_col : str
        Column to use for y-axis
    hue_col : str
        Column to use for color grouping
    title : str
        Plot title
    
    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Get unique values for hue column
    hue_values = results_df[hue_col].unique()
    
    # Plot each group
    for hue_val in hue_values:
        group_df = results_df[results_df[hue_col] == hue_val]
        ax.plot(group_df[x_col], group_df[y_col], 'o-', label=hue_val)
    
    # Add best point
    best_idx = results_df[y_col].idxmin()
    best_x = results_df.loc[best_idx, x_col]
    best_y = results_df.loc[best_idx, y_col]
    best_hue = results_df.loc[best_idx, hue_col]
    
    ax.scatter([best_x], [best_y], color='red', s=100, zorder=5, 
               label=f'Best: {best_hue}, {x_col}={best_x}, {y_col}={best_y:.4f}')
    
    # Add labels and title
    ax.set_xlabel(x_col)
    ax.set_ylabel(y_col)
    ax.set_title(title)
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.legend()
    
    return fig

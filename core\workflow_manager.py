"""
Workflow Manager for MELK Chemo Copilot

Orchestrates the workflow steps and manages navigation.
"""

import streamlit as st
from typing import Dict, Type, List, Any
from core.session_manager import session_manager
from config.settings import WORKFLOW_STEPS


class WorkflowManager:
    """Manages the workflow execution and navigation."""

    def __init__(self):
        self.steps: Dict[int, Type] = {}
        self.session = session_manager

    def register_step(self, step_number: int, step_class: Type) -> None:
        """Register a step class with the workflow manager."""
        self.steps[step_number] = step_class

    def get_current_step(self) -> int:
        """Get the current step number."""
        return self.session.get_current_step()

    def execute_current_step(self) -> None:
        """Execute the current workflow step."""
        current_step = self.get_current_step()

        if current_step in self.steps:
            step_class = self.steps[current_step]
            step_instance = step_class()
            step_instance.render()
        else:
            st.error(f"Step {current_step} is not implemented yet.")

    def render_sidebar(self) -> None:
        """Render the enhanced workflow navigation sidebar."""
        st.markdown("## 🗺️ Workflow Navigation")

        current_step = self.get_current_step()
        progress = self.session.get_workflow_progress()

        # Render dynamic step navigation
        for step_num in range(1, 8):
            step_name = WORKFLOW_STEPS.get(step_num, f"Step {step_num}")

            # Determine step status and styling
            if step_num == current_step:
                status_icon = "🔵"
                css_class = "current"
                button_style = "primary"
            elif progress.get(f"step_{step_num}", False):
                status_icon = "✅"
                css_class = "completed"
                button_style = "secondary"
            elif self.session.can_access_step(step_num):
                status_icon = "⚪"
                css_class = "available"
                button_style = "tertiary"
            else:
                status_icon = "🔒"
                css_class = "locked"
                button_style = "tertiary"

            # Create enhanced navigation button
            button_text = f"{status_icon} Step {step_num}: {step_name}"

            # Custom button with enhanced styling
            if self.session.can_access_step(step_num):
                if st.button(
                    button_text,
                    key=f"nav_step_{step_num}",
                    use_container_width=True,
                    type=button_style
                ):
                    self.session.set_current_step(step_num)
                    st.rerun()
            else:
                # Disabled button for locked steps
                st.markdown(f"""
                <div style="
                    padding: 0.75rem 1rem;
                    margin: 0.25rem 0;
                    border: 2px solid #adb5bd;
                    border-radius: 25px;
                    background: #f8f9fa;
                    color: #adb5bd;
                    opacity: 0.6;
                    font-family: 'Inter', sans-serif;
                    font-weight: 600;
                ">
                    {button_text}
                </div>
                """, unsafe_allow_html=True)

        # Add workflow progress
        st.markdown("---")
        self._render_progress_summary()

    def _render_progress_summary(self) -> None:
        """Render enhanced workflow progress summary."""
        st.markdown("### 📊 Progress Summary")

        progress = self.session.get_workflow_progress()
        completed_steps = sum(1 for completed in progress.values() if completed)
        total_steps = len(progress)

        progress_percentage = completed_steps / total_steps
        st.progress(progress_percentage)

        # Enhanced progress display
        st.markdown(f"""
        <div style="
            text-align: center;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 1.1rem;
            color: #2E8B57;
            margin: 1rem 0;
        ">
            {completed_steps}/{total_steps} Steps Completed
        </div>
        """, unsafe_allow_html=True)

        # Show data summary with enhanced styling
        data_summary = self.session.get_data_summary()

        if "x_train_shape" in data_summary:
            st.markdown("#### 📁 Data Status")
            st.markdown(f"🔹 **Training:** {data_summary['x_train_shape']}")
            if "x_test_shape" in data_summary:
                st.markdown(f"🔹 **Test:** {data_summary['x_test_shape']}")

        # Show current selections with icons
        selections_made = False
        if data_summary.get("preprocessing_method") != "Not selected":
            if not selections_made:
                st.markdown("#### ⚙️ Current Selections")
                selections_made = True
            st.markdown(f"🔸 **Preprocessing:** {data_summary['preprocessing_method']}")

        if data_summary.get("pls_algorithm") != "Not selected":
            if not selections_made:
                st.markdown("#### ⚙️ Current Selections")
                selections_made = True
            st.markdown(f"🔸 **PLS Algorithm:** {data_summary['pls_algorithm']}")

        if data_summary.get("cv_method") != "Not selected":
            if not selections_made:
                st.markdown("#### ⚙️ Current Selections")
                selections_made = True
            st.markdown(f"🔸 **CV Method:** {data_summary['cv_method']}")

    def validate_workflow_integrity(self) -> List[str]:
        """Validate the integrity of the workflow."""
        issues = []

        # Check data consistency
        data_issues = self.session.validate_data_consistency()
        issues.extend(data_issues)

        # Check step registration
        for step_num in range(1, 9):
            if step_num not in self.steps:
                issues.append(f"Step {step_num} is not registered")

        return issues

    def get_step_dependencies(self, step: int) -> List[int]:
        """Get the dependencies for a specific step."""
        dependencies = {
            1: [],
            2: [1],
            3: [1],
            4: [1, 3],
            5: [1, 3, 4],
            6: [1, 3, 4, 5],
            7: [1, 3, 4, 5, 6],
            8: [1, 2, 3, 4, 5, 6, 7]
        }

        return dependencies.get(step, [])

    def can_proceed_to_step(self, step: int) -> bool:
        """Check if workflow can proceed to a specific step."""
        dependencies = self.get_step_dependencies(step)
        progress = self.session.get_workflow_progress()

        for dep_step in dependencies:
            if not progress.get(f"step_{dep_step}", False):
                return False

        return True

    def get_next_incomplete_step(self) -> int:
        """Get the next incomplete step in the workflow."""
        progress = self.session.get_workflow_progress()

        for step_num in range(1, 9):
            if not progress.get(f"step_{step_num}", False):
                return step_num

        return 8  # All steps completed, return last step


# Create a global workflow manager instance
workflow_manager = WorkflowManager()

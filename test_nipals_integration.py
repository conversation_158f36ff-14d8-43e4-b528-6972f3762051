"""
Quick test to verify NIPALS integration in MELK Chemo Copilot
"""

import sys
import os
sys.path.insert(0, os.getcwd())

def test_nipals_integration():
    """Test the NIPALS integration."""
    try:
        from utils.nipals_pls import NIPALSRegression
        from sklearn.datasets import make_regression
        from sklearn.cross_decomposition import PLSRegression
        import numpy as np
        
        print("🧪 Testing True NIPALS Integration in MELK Chemo Copilot")
        print("=" * 60)
        
        # Generate test data
        X, y = make_regression(n_samples=100, n_features=50, noise=0.1, random_state=42)
        
        print(f"📊 Test Data: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Test True NIPALS
        print("\n🔄 Testing True NIPALS (PLS1)...")
        nipals_model = NIPALSRegression(n_components=3, mode='PLS1')
        nipals_model.fit(X, y)
        nipals_pred = nipals_model.predict(X)
        nipals_r2 = nipals_model.score(X, y)
        nipals_info = nipals_model.get_model_info()
        
        print(f"✅ True NIPALS: R² = {nipals_r2:.4f}")
        print(f"   Algorithm: {nipals_info['algorithm']}")
        print(f"   Mode: {nipals_info['mode']}")
        print(f"   Iterations: {nipals_info['n_iter_per_component']}")
        print(f"   Converged: {nipals_info['converged']}")
        
        # Test SIMPLS for comparison
        print("\n⚡ Testing SIMPLS (scikit-learn)...")
        simpls_model = PLSRegression(n_components=3)
        simpls_model.fit(X, y)
        simpls_pred = simpls_model.predict(X)
        simpls_r2 = simpls_model.score(X, y)
        
        print(f"✅ SIMPLS: R² = {simpls_r2:.4f}")
        
        # Compare results
        print(f"\n📊 Comparison:")
        print(f"   True NIPALS R²: {nipals_r2:.4f}")
        print(f"   SIMPLS R²:      {simpls_r2:.4f}")
        print(f"   Difference:     {abs(nipals_r2 - simpls_r2):.4f}")
        
        if abs(nipals_r2 - simpls_r2) < 0.1:
            print("✅ Results are similar (expected for well-conditioned data)")
        else:
            print("⚠️ Results differ (may indicate algorithmic differences)")
        
        # Test PLS2 mode
        print("\n🎯 Testing True NIPALS (PLS2) with multi-target...")
        y_multi = np.column_stack([y, y + np.random.normal(0, 0.1, len(y))])
        nipals_pls2 = NIPALSRegression(n_components=3, mode='PLS2')
        nipals_pls2.fit(X, y_multi)
        nipals_pls2_r2 = nipals_pls2.score(X, y_multi)
        nipals_pls2_info = nipals_pls2.get_model_info()
        
        print(f"✅ True NIPALS (PLS2): R² = {nipals_pls2_r2:.4f}")
        print(f"   Iterations: {nipals_pls2_info['n_iter_per_component']}")
        
        print("\n🎉 Integration Test Results:")
        print("✅ True NIPALS implementation is working")
        print("✅ Both PLS1 and PLS2 modes are functional")
        print("✅ Algorithm provides detailed model information")
        print("✅ Ready for use in MELK Chemo Copilot Step 5")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_nipals_integration()
    if success:
        print("\n🚀 NIPALS integration successful!")
    else:
        print("\n💥 NIPALS integration failed!")

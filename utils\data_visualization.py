"""
Utility functions for enhanced data visualization in the PLS Regression Analysis Tool.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st

def create_styled_dataframe(df, title=None, color_scheme='Blues'):
    """
    Create a styled dataframe with color gradients.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame to style
    title : str, optional
        Title for the dataframe
    color_scheme : str, optional
        Color scheme for styling (default: 'Blues')

    Returns:
    --------
    styled_df : pandas.io.formats.style.Styler
        Styled dataframe
    """
    # Create a copy of the dataframe
    styled_df = df.copy()

    # Apply styling
    styled = styled_df.style.background_gradient(cmap=color_scheme)

    # Add title if provided
    if title:
        styled = styled.set_caption(title)

    # Format numbers
    styled = styled.format(precision=3)

    return styled

def plot_spectra_train(x_train, x_axis_col=None, title="X_train Spectra Overlay"):
    """
    Create an overlay plot of X_train spectra.

    Parameters:
    -----------
    x_train : pandas.DataFrame
        X_train data with wavelengths as columns and samples as rows
    x_axis_col : str, optional
        Column to use as x-axis (wavelength, wavenumber, etc.)
    title : str, optional
        Plot title

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    # Create figure
    fig = go.Figure()

    # Get numeric columns for plotting
    x_train_numeric = x_train.select_dtypes(include=[np.number])
    if x_train_numeric.empty:
        x_train_numeric = x_train.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    # Extract wavelength values from column names
    try:
        # Try to convert column names to numeric values (assuming they are wavelengths)
        wavelengths = pd.to_numeric(x_train_numeric.columns, errors='coerce')
        # Remove NaN values (non-numeric column names)
        wavelengths = wavelengths[~np.isnan(wavelengths)]

        if len(wavelengths) > 0:
            # Use the numeric column names as wavelength values
            x_values = wavelengths
        else:
            # If no numeric column names, use column indices
            x_values = np.arange(len(x_train_numeric.columns))
    except:
        # Fallback to using column indices
        x_values = np.arange(len(x_train_numeric.columns))

    # Generate a color palette
    colors = px.colors.qualitative.Plotly

    # Add traces for each training sample
    for i in range(min(len(x_train_numeric), 20)):  # Show up to 20 samples
        # Get the actual sample name from the index
        try:
            sample_name = str(x_train_numeric.index[i])
        except:
            sample_name = f"Sample {i+1}"

        fig.add_trace(go.Scatter(
            x=x_values,
            y=x_train_numeric.iloc[i].values,
            mode='lines',
            name=sample_name,
            line=dict(color=colors[i % len(colors)], width=1.5),
            opacity=0.8
        ))

    # Add mean spectrum
    fig.add_trace(go.Scatter(
        x=x_values,
        y=x_train_numeric.mean().values,
        mode='lines',
        name='Mean Spectrum',
        line=dict(color='black', width=3, dash='dash'),
        opacity=1
    ))

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Wavelength (nm)",
        yaxis_title="Absorbance",
        legend_title="Samples",
        template="plotly_white",
        height=600,
        legend=dict(
            itemsizing='constant',
            title_font=dict(size=14),
            font=dict(size=12),
            borderwidth=1,
            bordercolor='#E2E2E2',
            bgcolor='rgba(255, 255, 255, 0.8)'
        ),
        margin=dict(l=50, r=50, t=80, b=50)
    )

    # Add range slider
    fig.update_layout(
        xaxis=dict(
            rangeslider=dict(visible=True),
            type="linear"
        )
    )

    return fig

def plot_spectra_test(x_test, x_axis_col=None, title="X_test Spectra Overlay"):
    """
    Create an overlay plot of X_test spectra.

    Parameters:
    -----------
    x_test : pandas.DataFrame
        X_test data with wavelengths as columns and samples as rows
    x_axis_col : str, optional
        Column to use as x-axis (wavelength, wavenumber, etc.)
    title : str, optional
        Plot title

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    # Create figure
    fig = go.Figure()

    # Get numeric columns for plotting
    x_test_numeric = x_test.select_dtypes(include=[np.number])
    if x_test_numeric.empty:
        x_test_numeric = x_test.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    # Extract wavelength values from column names
    try:
        # Try to convert column names to numeric values (assuming they are wavelengths)
        wavelengths = pd.to_numeric(x_test_numeric.columns, errors='coerce')
        # Remove NaN values (non-numeric column names)
        wavelengths = wavelengths[~np.isnan(wavelengths)]

        if len(wavelengths) > 0:
            # Use the numeric column names as wavelength values
            x_values = wavelengths
        else:
            # If no numeric column names, use column indices
            x_values = np.arange(len(x_test_numeric.columns))
    except:
        # Fallback to using column indices
        x_values = np.arange(len(x_test_numeric.columns))

    # Generate a color palette
    colors = px.colors.qualitative.Dark24

    # Add traces for each test sample
    for i in range(min(len(x_test_numeric), 20)):  # Show up to 20 samples
        # Get the actual sample name from the index
        try:
            sample_name = str(x_test_numeric.index[i])
        except:
            sample_name = f"Sample {i+1}"

        fig.add_trace(go.Scatter(
            x=x_values,
            y=x_test_numeric.iloc[i].values,
            mode='lines',
            name=sample_name,
            line=dict(color=colors[i % len(colors)], width=1.5),
            opacity=0.8
        ))

    # Add mean spectrum
    fig.add_trace(go.Scatter(
        x=x_values,
        y=x_test_numeric.mean().values,
        mode='lines',
        name='Mean Spectrum',
        line=dict(color='black', width=3, dash='dash'),
        opacity=1
    ))

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Wavelength (nm)",
        yaxis_title="Absorbance",
        legend_title="Samples",
        template="plotly_white",
        height=600,
        legend=dict(
            itemsizing='constant',
            title_font=dict(size=14),
            font=dict(size=12),
            borderwidth=1,
            bordercolor='#E2E2E2',
            bgcolor='rgba(255, 255, 255, 0.8)'
        ),
        margin=dict(l=50, r=50, t=80, b=50)
    )

    # Add range slider
    fig.update_layout(
        xaxis=dict(
            rangeslider=dict(visible=True),
            type="linear"
        )
    )

    return fig

def plot_spectra_overlay(x_train, x_test, x_axis_col=None, title="Spectra Overlay"):
    """
    Create an overlay plot of X_train and X_test spectra.

    Parameters:
    -----------
    x_train : pandas.DataFrame
        X_train data with wavelengths as columns and samples as rows
    x_test : pandas.DataFrame
        X_test data with wavelengths as columns and samples as rows
    x_axis_col : str, optional
        Column to use as x-axis (wavelength, wavenumber, etc.)
    title : str, optional
        Plot title

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    # Create figure
    fig = go.Figure()

    # Get numeric columns for plotting
    x_train_numeric = x_train.select_dtypes(include=[np.number])
    if x_train_numeric.empty:
        x_train_numeric = x_train.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    x_test_numeric = x_test.select_dtypes(include=[np.number])
    if x_test_numeric.empty:
        x_test_numeric = x_test.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    # Extract wavelength values from column names
    try:
        # Try to convert column names to numeric values (assuming they are wavelengths)
        wavelengths = pd.to_numeric(x_train_numeric.columns, errors='coerce')
        # Remove NaN values (non-numeric column names)
        wavelengths = wavelengths[~np.isnan(wavelengths)]

        if len(wavelengths) > 0:
            # Use the numeric column names as wavelength values
            x_values = wavelengths
        else:
            # If no numeric column names, use column indices
            x_values = np.arange(len(x_train_numeric.columns))
    except:
        # Fallback to using column indices
        x_values = np.arange(len(x_train_numeric.columns))

    # Add traces for each training sample (with low opacity)
    for i in range(min(len(x_train_numeric), 10)):  # Limit to 10 samples for clarity
        # Get the actual sample name from the index
        try:
            sample_name = f"Train: {str(x_train_numeric.index[i])}"
        except:
            sample_name = f"Train Sample {i+1}"

        fig.add_trace(go.Scatter(
            x=x_values,
            y=x_train_numeric.iloc[i].values,
            mode='lines',
            name=sample_name,
            line=dict(color='blue', width=1),
            opacity=0.3
        ))

    # Add traces for each test sample (with higher opacity)
    for i in range(min(len(x_test_numeric), 5)):  # Limit to 5 samples for clarity
        # Get the actual sample name from the index
        try:
            sample_name = f"Test: {str(x_test_numeric.index[i])}"
        except:
            sample_name = f"Test Sample {i+1}"

        fig.add_trace(go.Scatter(
            x=x_values,
            y=x_test_numeric.iloc[i].values,
            mode='lines',
            name=sample_name,
            line=dict(color='red', width=2),
            opacity=0.7
        ))

    # Add mean spectra
    fig.add_trace(go.Scatter(
        x=x_values,
        y=x_train_numeric.mean().values,
        mode='lines',
        name='Train Mean',
        line=dict(color='blue', width=3),
        opacity=1
    ))

    fig.add_trace(go.Scatter(
        x=x_values,
        y=x_test_numeric.mean().values,
        mode='lines',
        name='Test Mean',
        line=dict(color='red', width=3),
        opacity=1
    ))

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Wavelength (nm)",
        yaxis_title="Absorbance",
        legend_title="Samples",
        template="plotly_white",
        height=500
    )

    return fig

def plot_concentration_comparison(y_train, y_test=None):
    """
    Create bar plots comparing concentrations in Y_train and Y_test.

    Parameters:
    -----------
    y_train : pandas.DataFrame
        Y_train data
    y_test : pandas.DataFrame, optional
        Y_test data

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    # Get numeric columns
    y_train_numeric = y_train.select_dtypes(include=[np.number])
    if y_train_numeric.empty:
        y_train_numeric = y_train.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    # Get analyte names
    analyte_names = y_train_numeric.columns

    # Create subplots - one for each analyte
    n_analytes = len(analyte_names)
    fig = make_subplots(rows=1, cols=n_analytes,
                        subplot_titles=[f"{name}" for name in analyte_names],
                        shared_yaxes=True)

    # Add data for each analyte
    for i, analyte in enumerate(analyte_names):
        col_idx = i + 1  # Plotly is 1-indexed

        # Add Y_train boxplot
        fig.add_trace(
            go.Box(
                y=y_train_numeric[analyte].values,
                name="Train",
                boxpoints='all',
                jitter=0.3,
                pointpos=-1.8,
                marker_color='blue',
                line_color='blue',
                showlegend=i==0,  # Only show legend for first analyte
            ),
            row=1, col=col_idx
        )

        # Add Y_test boxplot if available
        if y_test is not None:
            y_test_numeric = y_test.select_dtypes(include=[np.number])
            if y_test_numeric.empty:
                y_test_numeric = y_test.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

            if analyte in y_test_numeric.columns:
                fig.add_trace(
                    go.Box(
                        y=y_test_numeric[analyte].values,
                        name="Test",
                        boxpoints='all',
                        jitter=0.3,
                        pointpos=1.8,
                        marker_color='red',
                        line_color='red',
                        showlegend=i==0,  # Only show legend for first analyte
                    ),
                    row=1, col=col_idx
                )

    # Update layout
    fig.update_layout(
        title="Concentration Distribution by Analyte",
        height=400,
        boxmode='group',
        template="plotly_white",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    return fig

def plot_correlation_heatmap(df, title="Correlation Heatmap"):
    """
    Create a correlation heatmap for the given dataframe.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame to create heatmap from
    title : str, optional
        Plot title

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    # Get numeric columns
    df_numeric = df.select_dtypes(include=[np.number])
    if df_numeric.empty:
        df_numeric = df.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    # Calculate correlation matrix
    corr_matrix = df_numeric.corr()

    # Create heatmap
    fig = px.imshow(
        corr_matrix,
        text_auto='.2f',
        color_continuous_scale='RdBu_r',
        zmin=-1, zmax=1,
        title=title
    )

    # Update layout
    fig.update_layout(
        height=500,
        template="plotly_white"
    )

    return fig

def plot_data_heatmap(df, title="Data Heatmap", color_scale="Viridis"):
    """
    Create a heatmap visualization of the data matrix.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame to create heatmap from
    title : str, optional
        Plot title
    color_scale : str, optional
        Color scale for the heatmap

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    # Get numeric columns
    df_numeric = df.select_dtypes(include=[np.number])
    if df_numeric.empty:
        df_numeric = df.iloc[:, 1:].apply(pd.to_numeric, errors='coerce')

    # Create sample labels for y-axis
    sample_labels = df_numeric.index.tolist()

    # Create variable labels for x-axis
    # Try to convert column names to numeric values (assuming they are wavelengths)
    try:
        wavelengths = pd.to_numeric(df_numeric.columns, errors='coerce')
        # Remove NaN values (non-numeric column names)
        wavelengths = wavelengths[~np.isnan(wavelengths)]

        if len(wavelengths) > 0:
            # Use the numeric column names as x-axis labels
            variable_labels = wavelengths.tolist()
        else:
            # Use column names as x-axis labels
            variable_labels = df_numeric.columns.tolist()
    except:
        # Fallback to using column names
        variable_labels = df_numeric.columns.tolist()

    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=df_numeric.values,
        x=variable_labels,
        y=sample_labels,
        colorscale=color_scale,
        colorbar=dict(
            title=dict(
                text="Absorbance",
                side="right",
                font=dict(size=14)
            )
        ),
        hovertemplate='Sample: %{y}<br>Wavelength: %{x}<br>Value: %{z:.4f}<extra></extra>'
    ))

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Wavelength (nm)",
        yaxis_title="Samples",
        template="plotly_white",
        height=600,
        margin=dict(l=50, r=50, t=80, b=50),
        xaxis=dict(
            type='linear',
            tickmode='auto',
            nticks=10,
            showgrid=True
        ),
        yaxis=dict(
            tickmode='array',
            tickvals=list(range(len(sample_labels))),
            ticktext=sample_labels
        )
    )

    return fig

def create_data_summary_card(df, title, color="#0066cc"):
    """
    Create a styled summary card for a dataframe.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame to summarize
    title : str
        Card title
    color : str, optional
        Card color

    Returns:
    --------
    html : str
        HTML for the card
    """
    # Get shape and column info
    rows, cols = df.shape

    # Create HTML for the card
    html = f"""
    <div style="
        border-radius: 5px;
        background-color: white;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-left: 5px solid {color};
    ">
        <h3 style="color: {color}; margin-top: 0;">{title}</h3>
        <p><strong>Shape:</strong> {rows} rows × {cols} columns</p>
        <p><strong>Memory usage:</strong> {df.memory_usage(deep=True).sum() / 1024:.2f} KB</p>
    </div>
    """

    return html

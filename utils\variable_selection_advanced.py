import numpy as np
import pandas as pd
from sklearn.cross_decomposition import PLSRegression
from sklearn.model_selection import <PERSON><PERSON><PERSON>
from sklearn.metrics import mean_squared_error
import random
from .model import perform_pls_cv, calculate_vip_scores

def apply_interval_pls(X, Y, n_intervals=10, n_components=None, n_splits=10, random_state=42):
    """
    Apply interval PLS (iPLS) for variable selection.

    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    n_intervals : int
        Number of intervals to divide X into
    n_components : int, optional
        Number of PLS components. If None, optimal number is determined by CV.
    n_splits : int
        Number of folds for cross-validation
    random_state : int
        Random seed for reproducibility

    Returns:
    --------
    best_interval : array
        Boolean mask of selected variables
    interval_results : dict
        Results for each interval
    """
    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values

    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)

    n_samples, n_variables = X.shape

    # Calculate interval size
    interval_size = n_variables // n_intervals

    # Initialize arrays to store results
    interval_masks = []
    interval_errors = np.zeros(n_intervals)

    # Perform iPLS for each interval
    for i in range(n_intervals):
        # Calculate interval boundaries
        start_idx = i * interval_size
        end_idx = (i + 1) * interval_size if i < n_intervals - 1 else n_variables

        # Create mask for current interval
        interval_mask = np.zeros(n_variables, dtype=bool)
        interval_mask[start_idx:end_idx] = True
        interval_masks.append(interval_mask)

        # Select variables for current interval
        X_interval = X[:, interval_mask]

        # Determine optimal number of components if not provided
        if n_components is None:
            cv_results = perform_pls_cv(X_interval, Y, max_components=min(10, X_interval.shape[1]), n_splits=n_splits, random_state=random_state)
            opt_comp = np.argmin(cv_results['rmsecv']) + 1
        else:
            opt_comp = min(n_components, X_interval.shape[1])

        # Initialize k-fold cross-validation
        kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)

        # Perform cross-validation
        fold_errors = np.zeros(n_splits)
        for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X_interval)):
            X_train, X_test = X_interval[train_idx], X_interval[test_idx]
            Y_train, Y_test = Y[train_idx], Y[test_idx]

            # Fit PLS model
            pls = PLSRegression(n_components=opt_comp)
            pls.fit(X_train, Y_train)

            # Predict test set
            Y_pred = pls.predict(X_test)

            # Calculate RMSE
            fold_errors[fold_idx] = np.sqrt(mean_squared_error(Y_test, Y_pred))

        # Store mean RMSE for current interval
        interval_errors[i] = np.mean(fold_errors)

    # Find best interval
    best_interval_idx = np.argmin(interval_errors)
    best_interval = interval_masks[best_interval_idx]

    # Prepare results
    interval_results = {
        'interval_errors': interval_errors,
        'interval_masks': interval_masks,
        'best_interval_idx': best_interval_idx
    }

    return best_interval, interval_results

def apply_backward_interval_pls(X, Y, n_intervals=10, n_components=None, n_splits=10, random_state=42):
    """
    Apply backward interval PLS (BiPLS) for variable selection.

    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    n_intervals : int
        Number of intervals to divide X into
    n_components : int, optional
        Number of PLS components. If None, optimal number is determined by CV.
    n_splits : int
        Number of folds for cross-validation
    random_state : int
        Random seed for reproducibility

    Returns:
    --------
    best_mask : array
        Boolean mask of selected variables
    bipls_results : dict
        Results of the BiPLS algorithm
    """
    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values

    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)

    n_samples, n_variables = X.shape

    # Calculate interval size
    interval_size = n_variables // n_intervals

    # Create initial mask (all variables selected)
    current_mask = np.ones(n_variables, dtype=bool)

    # Create interval masks
    interval_masks = []
    for i in range(n_intervals):
        # Calculate interval boundaries
        start_idx = i * interval_size
        end_idx = (i + 1) * interval_size if i < n_intervals - 1 else n_variables

        # Create mask for current interval
        interval_mask = np.zeros(n_variables, dtype=bool)
        interval_mask[start_idx:end_idx] = True
        interval_masks.append(interval_mask)

    # Initialize arrays to store results
    removed_intervals = []
    error_history = []

    # Determine optimal number of components for full model if not provided
    if n_components is None:
        cv_results = perform_pls_cv(X, Y, max_components=min(10, X.shape[1]), n_splits=n_splits, random_state=random_state)
        full_opt_comp = np.argmin(cv_results['rmsecv']) + 1
        full_error = np.min(cv_results['rmsecv'])
    else:
        full_opt_comp = n_components

        # Calculate full model error
        kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
        fold_errors = np.zeros(n_splits)

        for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X)):
            X_train, X_test = X[train_idx], X[test_idx]
            Y_train, Y_test = Y[train_idx], Y[test_idx]

            # Fit PLS model
            pls = PLSRegression(n_components=full_opt_comp)
            pls.fit(X_train, Y_train)

            # Predict test set
            Y_pred = pls.predict(X_test)

            # Calculate RMSE
            fold_errors[fold_idx] = np.sqrt(mean_squared_error(Y_test, Y_pred))

        full_error = np.mean(fold_errors)

    # Store initial error
    error_history.append(full_error)

    # Backward elimination
    for _ in range(n_intervals - 1):
        interval_errors = np.zeros(n_intervals)

        # Try removing each remaining interval
        for i in range(n_intervals):
            # Skip if interval already removed
            if i in removed_intervals:
                interval_errors[i] = np.inf
                continue

            # Create temporary mask without current interval
            temp_mask = current_mask.copy()
            temp_mask[interval_masks[i]] = False

            # Select variables
            X_temp = X[:, temp_mask]

            # Determine optimal number of components
            if n_components is None:
                cv_results = perform_pls_cv(X_temp, Y, max_components=min(10, X_temp.shape[1]), n_splits=n_splits, random_state=random_state)
                opt_comp = np.argmin(cv_results['rmsecv']) + 1
                interval_errors[i] = np.min(cv_results['rmsecv'])
            else:
                opt_comp = min(n_components, X_temp.shape[1])

                # Calculate error
                kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
                fold_errors = np.zeros(n_splits)

                for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X_temp)):
                    X_train, X_test = X_temp[train_idx], X_temp[test_idx]
                    Y_train, Y_test = Y[train_idx], Y[test_idx]

                    # Fit PLS model
                    pls = PLSRegression(n_components=opt_comp)
                    pls.fit(X_train, Y_train)

                    # Predict test set
                    Y_pred = pls.predict(X_test)

                    # Calculate RMSE
                    fold_errors[fold_idx] = np.sqrt(mean_squared_error(Y_test, Y_pred))

                interval_errors[i] = np.mean(fold_errors)

        # Find best interval to remove
        best_interval_idx = np.argmin(interval_errors)
        best_error = interval_errors[best_interval_idx]

        # Update mask and results
        current_mask[interval_masks[best_interval_idx]] = False
        removed_intervals.append(best_interval_idx)
        error_history.append(best_error)

    # Find best model (minimum error)
    best_model_idx = np.argmin(error_history)

    # Reconstruct best mask
    if best_model_idx == 0:
        # Full model is best
        best_mask = np.ones(n_variables, dtype=bool)
    else:
        # Some intervals were removed
        best_mask = np.ones(n_variables, dtype=bool)
        for i in removed_intervals[:best_model_idx]:
            best_mask[interval_masks[i]] = False

    # Prepare results
    bipls_results = {
        'error_history': error_history,
        'removed_intervals': removed_intervals,
        'best_model_idx': best_model_idx
    }

    return best_mask, bipls_results

def apply_moving_window_pls(X, Y, window_size=20, step_size=5, n_components=None, n_splits=10, random_state=42):
    """
    Apply moving window PLS (MWPLS) for variable selection.

    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    window_size : int
        Size of the moving window
    step_size : int
        Step size for moving the window
    n_components : int, optional
        Number of PLS components. If None, optimal number is determined by CV.
    n_splits : int
        Number of folds for cross-validation
    random_state : int
        Random seed for reproducibility

    Returns:
    --------
    best_window : array
        Boolean mask of selected variables
    mwpls_results : dict
        Results of the MWPLS algorithm
    """
    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values

    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)

    n_samples, n_variables = X.shape

    # Check if window size is valid
    if window_size > n_variables:
        window_size = n_variables

    # Calculate number of windows
    n_windows = (n_variables - window_size) // step_size + 1

    # Initialize arrays to store results
    window_masks = []
    window_errors = np.zeros(n_windows)

    # Perform MWPLS for each window
    for i in range(n_windows):
        # Calculate window boundaries
        start_idx = i * step_size
        end_idx = start_idx + window_size

        # Create mask for current window
        window_mask = np.zeros(n_variables, dtype=bool)
        window_mask[start_idx:end_idx] = True
        window_masks.append(window_mask)

        # Select variables for current window
        X_window = X[:, window_mask]

        # Determine optimal number of components if not provided
        if n_components is None:
            cv_results = perform_pls_cv(X_window, Y, max_components=min(10, X_window.shape[1]), n_splits=n_splits, random_state=random_state)
            opt_comp = np.argmin(cv_results['rmsecv']) + 1
            window_errors[i] = np.min(cv_results['rmsecv'])
        else:
            opt_comp = min(n_components, X_window.shape[1])

            # Calculate error
            kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
            fold_errors = np.zeros(n_splits)

            for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X_window)):
                X_train, X_test = X_window[train_idx], X_window[test_idx]
                Y_train, Y_test = Y[train_idx], Y[test_idx]

                # Fit PLS model
                pls = PLSRegression(n_components=opt_comp)
                pls.fit(X_train, Y_train)

                # Predict test set
                Y_pred = pls.predict(X_test)

                # Calculate RMSE
                fold_errors[fold_idx] = np.sqrt(mean_squared_error(Y_test, Y_pred))

            window_errors[i] = np.mean(fold_errors)

    # Find best window
    best_window_idx = np.argmin(window_errors)
    best_window = window_masks[best_window_idx]

    # Prepare results
    mwpls_results = {
        'window_errors': window_errors,
        'window_masks': window_masks,
        'best_window_idx': best_window_idx
    }

    return best_window, mwpls_results

def apply_cars(X, Y, n_components=None, n_splits=10, n_sampling=50, frac_initial=0.8, frac_final=0.2, random_state=42):
    """
    Apply Competitive Adaptive Reweighted Sampling (CARS) for variable selection.

    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    n_components : int, optional
        Number of PLS components. If None, optimal number is determined by CV.
    n_splits : int
        Number of folds for cross-validation
    n_sampling : int
        Number of sampling runs
    frac_initial : float
        Initial fraction of variables to keep
    frac_final : float
        Final fraction of variables to keep
    random_state : int
        Random seed for reproducibility

    Returns:
    --------
    best_subset : array
        Boolean mask of selected variables
    cars_results : dict
        Results of the CARS algorithm
    """
    # Set random seed
    np.random.seed(random_state)
    random.seed(random_state)

    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values

    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)

    n_samples, n_variables = X.shape

    # Determine optimal number of components if not provided
    if n_components is None:
        cv_results = perform_pls_cv(X, Y, max_components=min(10, X.shape[1]), n_splits=n_splits, random_state=random_state)
        n_components = np.argmin(cv_results['rmsecv']) + 1

    # Initialize arrays to store results
    subset_masks = []
    subset_errors = np.zeros(n_sampling)

    # Calculate exponential decay function for variable reduction
    b = np.log(frac_initial / frac_final) / (n_sampling - 1)
    a = frac_initial * np.exp(b)

    # Fit initial PLS model to get variable importance
    pls = PLSRegression(n_components=n_components)
    pls.fit(X, Y)

    # Calculate VIP scores
    vip_scores = calculate_vip_scores(pls, X)

    # Perform CARS algorithm
    for i in range(n_sampling):
        # Calculate number of variables to keep
        k = int(a * np.exp(-b * i) * n_variables)
        k = max(1, min(k, n_variables))  # Ensure at least 1 variable

        # Create weights based on VIP scores
        weights = vip_scores ** 2

        # Normalize weights
        weights = weights / np.sum(weights)

        # Sample variables based on weights
        selected_indices = np.random.choice(n_variables, size=k, replace=False, p=weights)

        # Create mask for current subset
        subset_mask = np.zeros(n_variables, dtype=bool)
        subset_mask[selected_indices] = True
        subset_masks.append(subset_mask)

        # Select variables for current subset
        X_subset = X[:, subset_mask]

        # Calculate error
        kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
        fold_errors = np.zeros(n_splits)

        for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X_subset)):
            X_train, X_test = X_subset[train_idx], X_subset[test_idx]
            Y_train, Y_test = Y[train_idx], Y[test_idx]

            # Fit PLS model
            pls_subset = PLSRegression(n_components=min(n_components, X_subset.shape[1]))
            pls_subset.fit(X_train, Y_train)

            # Predict test set
            Y_pred = pls_subset.predict(X_test)

            # Calculate RMSE
            fold_errors[fold_idx] = np.sqrt(mean_squared_error(Y_test, Y_pred))

        subset_errors[i] = np.mean(fold_errors)

    # Find best subset
    best_subset_idx = np.argmin(subset_errors)
    best_subset = subset_masks[best_subset_idx]

    # Prepare results
    cars_results = {
        'subset_errors': subset_errors,
        'subset_masks': subset_masks,
        'best_subset_idx': best_subset_idx,
        'vip_scores': vip_scores
    }

    return best_subset, cars_results

def apply_uve(X, Y, n_components=None, n_splits=10, n_iterations=100, alpha=0.95, random_state=42):
    """
    Apply Uninformative Variable Elimination (UVE) for variable selection.

    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    n_components : int, optional
        Number of PLS components. If None, optimal number is determined by CV.
    n_splits : int
        Number of folds for cross-validation
    n_iterations : int
        Number of iterations for stability calculation
    alpha : float
        Confidence level for variable selection
    random_state : int
        Random seed for reproducibility

    Returns:
    --------
    selected_vars : array
        Boolean mask of selected variables
    uve_results : dict
        Results of the UVE algorithm
    """
    # Set random seed
    np.random.seed(random_state)

    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values

    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)

    n_samples, n_variables = X.shape

    # Determine optimal number of components if not provided
    if n_components is None:
        cv_results = perform_pls_cv(X, Y, max_components=min(10, X.shape[1]), n_splits=n_splits, random_state=random_state)
        n_components = np.argmin(cv_results['rmsecv']) + 1

    # Create artificial variables
    X_artificial = np.random.randn(n_samples, n_variables)

    # Combine real and artificial variables
    X_combined = np.hstack((X, X_artificial))

    # Initialize array to store regression coefficients
    coefs = np.zeros((n_iterations, 2 * n_variables))

    # Perform UVE algorithm
    for i in range(n_iterations):
        # Create random subset of samples
        subset_idx = np.random.choice(n_samples, size=int(0.8 * n_samples), replace=False)

        # Fit PLS model
        pls = PLSRegression(n_components=n_components)
        pls.fit(X_combined[subset_idx], Y[subset_idx])

        # Store regression coefficients
        coefs[i] = pls.coef_.ravel()

    # Calculate mean and standard deviation of coefficients
    coef_mean = np.mean(coefs, axis=0)
    coef_std = np.std(coefs, axis=0)

    # Calculate reliability measure
    reliability = np.abs(coef_mean) / coef_std

    # Split reliability for real and artificial variables
    real_reliability = reliability[:n_variables]
    artificial_reliability = reliability[n_variables:]

    # Calculate threshold based on artificial variables
    threshold = np.percentile(artificial_reliability, alpha * 100)

    # Select variables with reliability above threshold
    selected_vars = real_reliability > threshold

    # Prepare results
    uve_results = {
        'reliability': real_reliability,
        'artificial_reliability': artificial_reliability,
        'threshold': threshold
    }

    return selected_vars, uve_results

def apply_selectivity_ratio(X, Y, n_components=None, threshold=1.0, random_state=42):
    """
    Apply Selectivity Ratio (SR) for variable selection.

    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    n_components : int, optional
        Number of PLS components. If None, optimal number is determined by CV.
    threshold : float
        Threshold for variable selection
    random_state : int
        Random seed for reproducibility

    Returns:
    --------
    selected_vars : array
        Boolean mask of selected variables
    sr_results : dict
        Results of the SR algorithm
    """
    # Set random seed
    np.random.seed(random_state)

    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values

    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)

    n_samples, n_variables = X.shape

    # Determine optimal number of components if not provided
    if n_components is None:
        cv_results = perform_pls_cv(X, Y, max_components=min(10, X.shape[1]), n_splits=10, random_state=random_state)
        n_components = np.argmin(cv_results['rmsecv']) + 1

    # Fit PLS model
    pls = PLSRegression(n_components=n_components)
    pls.fit(X, Y)

    # Calculate target-projected data
    T = pls.x_scores_
    P = pls.x_loadings_
    W = pls.x_weights_

    # Calculate target-projected loadings
    P_tp = np.zeros_like(P)
    for i in range(n_components):
        P_tp[:, i] = X.T @ T[:, i] / (T[:, i].T @ T[:, i])

    # Calculate target-projected data
    X_tp = T @ P_tp.T

    # Calculate explained variance for each variable
    explained_var = np.var(X_tp, axis=0)

    # Calculate residual variance for each variable
    residual_var = np.var(X - X_tp, axis=0)

    # Calculate selectivity ratio
    selectivity_ratio = explained_var / residual_var

    # Select variables with selectivity ratio above threshold
    selected_vars = selectivity_ratio > threshold

    # Prepare results
    sr_results = {
        'selectivity_ratio': selectivity_ratio,
        'threshold': threshold
    }

    return selected_vars, sr_results

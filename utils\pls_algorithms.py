import numpy as np
from sklearn.cross_decomposition import P<PERSON><PERSON><PERSON>ression
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.utils.validation import check_array, check_X_y, check_is_fitted
from sklearn.decomposition import KernelPCA
from scipy.linalg import svd

class NIPALS_PLS(BaseEstimator, RegressorMixin):
    """
    NIPALS (Nonlinear Iterative Partial Least Squares) implementation of PLS.

    Parameters:
    -----------
    n_components : int
        Number of components to keep.
    max_iter : int
        Maximum number of iterations.
    tol : float
        Tolerance used in the iterative algorithm.
    copy : bool
        Whether to copy X and Y.
    """
    def __init__(self, n_components=2, max_iter=500, tol=1e-6, copy=True):
        self.n_components = n_components
        self.max_iter = max_iter
        self.tol = tol
        self.copy = copy

    def fit(self, X, Y):
        """
        Fit model to data.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.
        Y : array-like
            Target vectors, where n_samples is the number of samples and
            n_targets is the number of response variables.

        Returns:
        --------
        self : object
            Returns self.
        """
        # Check input data
        X, Y = check_X_y(X, Y, copy=self.copy, multi_output=True)

        n_samples, n_features = X.shape
        _, n_targets = Y.shape

        # Initialize arrays
        self.x_weights_ = np.zeros((n_features, self.n_components))
        self.y_weights_ = np.zeros((n_targets, self.n_components))
        self.x_loadings_ = np.zeros((n_features, self.n_components))
        self.y_loadings_ = np.zeros((n_targets, self.n_components))
        self.x_scores_ = np.zeros((n_samples, self.n_components))
        self.y_scores_ = np.zeros((n_samples, self.n_components))

        # Center X and Y
        X_mean = np.mean(X, axis=0)
        Y_mean = np.mean(Y, axis=0)
        X_centered = X - X_mean
        Y_centered = Y - Y_mean

        # Store means for prediction
        self.x_mean_ = X_mean
        self.y_mean_ = Y_mean

        # Compute NIPALS algorithm
        X_residual = X_centered.copy()
        Y_residual = Y_centered.copy()

        for i in range(self.n_components):
            # Initialize u as the first column of Y
            u = Y_residual[:, 0].reshape(-1, 1)

            for _ in range(self.max_iter):
                # Calculate w (X weights)
                w = X_residual.T @ u
                w = w / np.linalg.norm(w)

                # Calculate t (X scores)
                t = X_residual @ w

                # Calculate c (Y weights)
                c = Y_residual.T @ t
                c = c / np.linalg.norm(c)

                # Calculate u (Y scores)
                u_new = Y_residual @ c

                # Check convergence
                if np.linalg.norm(u - u_new) < self.tol:
                    break

                u = u_new

            # Calculate p (X loadings)
            p = (X_residual.T @ t) / (t.T @ t)

            # Calculate q (Y loadings)
            q = (Y_residual.T @ t) / (t.T @ t)

            # Deflate X and Y
            X_residual = X_residual - t @ p.T
            Y_residual = Y_residual - t @ q.T

            # Store results
            self.x_weights_[:, i] = w.ravel()
            self.y_weights_[:, i] = c.ravel()
            self.x_loadings_[:, i] = p.ravel()
            self.y_loadings_[:, i] = q.ravel()
            self.x_scores_[:, i] = t.ravel()
            self.y_scores_[:, i] = u.ravel()

        # Calculate regression coefficients
        self.coef_ = np.zeros((n_features, n_targets))
        for i in range(self.n_components):
            self.coef_ += np.outer(self.x_weights_[:, i], self.y_loadings_[:, i])

        return self

    def transform(self, X):
        """
        Apply the dimension reduction.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        x_scores : array
            X scores.
        """
        check_is_fitted(self, 'x_weights_')
        X = check_array(X, copy=self.copy)

        # Center X
        X_centered = X - self.x_mean_

        # Calculate scores
        x_scores = np.zeros((X.shape[0], self.n_components))
        for i in range(self.n_components):
            x_scores[:, i] = X_centered @ self.x_weights_[:, i]

        return x_scores

    def predict(self, X):
        """
        Predict using the model.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        Y_pred : array
            Predicted values.
        """
        check_is_fitted(self, 'coef_')
        X = check_array(X, copy=self.copy)

        # Center X
        X_centered = X - self.x_mean_

        # Predict
        Y_pred = X_centered @ self.coef_ + self.y_mean_

        return Y_pred

class SIMPLS_PLS(BaseEstimator, RegressorMixin):
    """
    SIMPLS (Statistically Inspired Modification of PLS) implementation.

    Parameters:
    -----------
    n_components : int
        Number of components to keep.
    copy : bool
        Whether to copy X and Y.
    """
    def __init__(self, n_components=2, copy=True):
        self.n_components = n_components
        self.copy = copy

    def fit(self, X, Y):
        """
        Fit model to data.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.
        Y : array-like
            Target vectors, where n_samples is the number of samples and
            n_targets is the number of response variables.

        Returns:
        --------
        self : object
            Returns self.
        """
        # Check input data
        X, Y = check_X_y(X, Y, copy=self.copy, multi_output=True)

        n_samples, n_features = X.shape
        _, n_targets = Y.shape

        # Center X and Y
        X_mean = np.mean(X, axis=0)
        Y_mean = np.mean(Y, axis=0)
        X_centered = X - X_mean
        Y_centered = Y - Y_mean

        # Store means for prediction
        self.x_mean_ = X_mean
        self.y_mean_ = Y_mean

        # Initialize arrays
        self.x_weights_ = np.zeros((n_features, self.n_components))
        self.y_weights_ = np.zeros((n_targets, self.n_components))
        self.x_scores_ = np.zeros((n_samples, self.n_components))
        self.x_loadings_ = np.zeros((n_features, self.n_components))
        self.y_loadings_ = np.zeros((n_targets, self.n_components))

        # Compute cross-covariance matrix
        S = X_centered.T @ Y_centered

        # Orthogonal basis for X space
        V = np.zeros((n_features, self.n_components))

        for i in range(self.n_components):
            # SVD of S
            U, D, Vt = np.linalg.svd(S, full_matrices=False)

            # X weights
            r = U[:, 0]
            self.x_weights_[:, i] = r

            # X scores
            t = X_centered @ r
            t_norm = np.linalg.norm(t)
            t = t / t_norm
            self.x_scores_[:, i] = t

            # X loadings
            p = X_centered.T @ t
            self.x_loadings_[:, i] = p

            # Y weights and loadings
            q = S.T @ r / t_norm
            self.y_weights_[:, i] = q
            self.y_loadings_[:, i] = q

            # Update orthogonal basis
            v = p.copy()
            if i > 0:
                v = v - V[:, :i] @ (V[:, :i].T @ p)
            v = v / np.linalg.norm(v)
            V[:, i] = v

            # Deflate S
            # Ensure dimensions match for matrix multiplication
            v_reshaped = v.reshape(-1, 1)
            S = S - v_reshaped @ (v_reshaped.T @ S)

        # Calculate regression coefficients
        self.coef_ = self.x_weights_ @ self.y_weights_.T

        return self

    def transform(self, X):
        """
        Apply the dimension reduction.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        x_scores : array
            X scores.
        """
        check_is_fitted(self, 'x_weights_')
        X = check_array(X, copy=self.copy)

        # Center X
        X_centered = X - self.x_mean_

        # Calculate scores
        x_scores = X_centered @ self.x_weights_

        return x_scores

    def predict(self, X):
        """
        Predict using the model.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        Y_pred : array
            Predicted values.
        """
        check_is_fitted(self, 'coef_')
        X = check_array(X, copy=self.copy)

        # Center X
        X_centered = X - self.x_mean_

        # Predict
        Y_pred = X_centered @ self.coef_ + self.y_mean_

        return Y_pred

class KernelPLS(BaseEstimator, RegressorMixin):
    """
    Kernel PLS implementation.

    Parameters:
    -----------
    n_components : int
        Number of components to keep.
    kernel : str
        Kernel type ('linear', 'poly', 'rbf', 'sigmoid', 'cosine', 'precomputed').
    gamma : float
        Kernel coefficient for 'rbf', 'poly' and 'sigmoid'.
    degree : int
        Degree for 'poly' kernel.
    coef0 : float
        Independent term in 'poly' and 'sigmoid' kernels.
    copy : bool
        Whether to copy X and Y.
    """
    def __init__(self, n_components=2, kernel='rbf', gamma=None, degree=3, coef0=1, copy=True):
        self.n_components = n_components
        self.kernel = kernel
        self.gamma = gamma
        self.degree = degree
        self.coef0 = coef0
        self.copy = copy

    def fit(self, X, Y):
        """
        Fit model to data.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.
        Y : array-like
            Target vectors, where n_samples is the number of samples and
            n_targets is the number of response variables.

        Returns:
        --------
        self : object
            Returns self.
        """
        # Check input data
        X, Y = check_X_y(X, Y, copy=self.copy, multi_output=True)

        n_samples, n_features = X.shape
        _, n_targets = Y.shape

        # Store original X for prediction
        self.X_train_ = X.copy()

        # Center Y
        self.y_mean_ = np.mean(Y, axis=0)
        Y_centered = Y - self.y_mean_

        # Create kernel PCA object
        self.kpca_ = KernelPCA(
            n_components=self.n_components,
            kernel=self.kernel,
            gamma=self.gamma,
            degree=self.degree,
            coef0=self.coef0,
            fit_inverse_transform=True
        )

        # Transform X to kernel space
        K = self.kpca_.fit_transform(X)

        # Fit PLS in kernel space
        try:
            self.pls_ = PLSRegression(n_components=min(self.n_components, K.shape[1], Y_centered.shape[1]))
            self.pls_.fit(K, Y_centered)
        except Exception as e:
            # Fallback to a more robust approach if SVD fails
            self.pls_ = PLSRegression(n_components=1)
            self.pls_.fit(K, Y_centered)

        return self

    def transform(self, X):
        """
        Apply the dimension reduction.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        x_scores : array
            X scores.
        """
        check_is_fitted(self, 'kpca_')
        X = check_array(X, copy=self.copy)

        # Transform X to kernel space
        K = self.kpca_.transform(X)

        return K

    def predict(self, X):
        """
        Predict using the model.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        Y_pred : array
            Predicted values.
        """
        check_is_fitted(self, 'pls_')
        X = check_array(X, copy=self.copy)

        # Transform X to kernel space
        K = self.kpca_.transform(X)

        # Predict using PLS
        Y_pred = self.pls_.predict(K) + self.y_mean_

        return Y_pred

class OPLS(BaseEstimator, RegressorMixin):
    """
    Orthogonal PLS (OPLS) implementation.

    Parameters:
    -----------
    n_components : int
        Number of components to keep.
    scale : bool
        Whether to scale the data.
    copy : bool
        Whether to copy X and Y.
    """
    def __init__(self, n_components=2, scale=True, copy=True):
        self.n_components = n_components
        self.scale = scale
        self.copy = copy

    def fit(self, X, Y):
        """
        Fit model to data.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.
        Y : array-like
            Target vectors, where n_samples is the number of samples and
            n_targets is the number of response variables.

        Returns:
        --------
        self : object
            Returns self.
        """
        # Check input data
        X, Y = check_X_y(X, Y, copy=self.copy, multi_output=True)

        # If multiple response variables, use only the first one
        # but warn the user
        if Y.shape[1] > 1:
            import warnings
            warnings.warn("OPLS works best with a single response variable. Using only the first response variable.")
            Y = Y[:, 0:1]

        n_samples, n_features = X.shape

        # Center X and Y
        self.x_mean_ = np.mean(X, axis=0)
        self.y_mean_ = np.mean(Y, axis=0)
        X_centered = X - self.x_mean_
        Y_centered = Y - self.y_mean_

        # Scale X and Y if requested
        if self.scale:
            self.x_std_ = np.std(X_centered, axis=0, ddof=1)
            self.x_std_[self.x_std_ == 0] = 1.0
            X_centered = X_centered / self.x_std_

            self.y_std_ = np.std(Y_centered, axis=0, ddof=1)
            self.y_std_[self.y_std_ == 0] = 1.0
            Y_centered = Y_centered / self.y_std_

        # Flatten Y for easier handling
        y = Y_centered.ravel()

        # Calculate first weight vector (predictive component)
        w = X_centered.T @ y
        w = w / np.linalg.norm(w)

        # Calculate first score vector
        t = X_centered @ w

        # Calculate Y loadings
        q = (y.T @ t) / (t.T @ t)

        # Calculate X loadings
        p = (X_centered.T @ t) / (t.T @ t)

        # Calculate orthogonal components
        W_ortho = np.zeros((n_features, self.n_components - 1))
        T_ortho = np.zeros((n_samples, self.n_components - 1))
        P_ortho = np.zeros((n_features, self.n_components - 1))

        X_residual = X_centered - t @ p.T

        for i in range(self.n_components - 1):
            # Calculate orthogonal weight
            w_ortho = X_residual.T @ t
            w_ortho = w_ortho / np.linalg.norm(w_ortho)

            # Calculate orthogonal score
            t_ortho = X_residual @ w_ortho

            # Calculate orthogonal loading
            p_ortho = (X_residual.T @ t_ortho) / (t_ortho.T @ t_ortho)

            # Store results
            W_ortho[:, i] = w_ortho
            T_ortho[:, i] = t_ortho
            P_ortho[:, i] = p_ortho

            # Deflate X
            X_residual = X_residual - t_ortho @ p_ortho.T

        # Store model parameters
        self.w_ = w
        self.t_ = t
        self.p_ = p
        self.q_ = q
        self.W_ortho_ = W_ortho
        self.T_ortho_ = T_ortho
        self.P_ortho_ = P_ortho

        # Calculate regression coefficient
        self.coef_ = w * q

        if self.scale:
            self.coef_ = self.coef_ / self.x_std_
            if self.y_std_[0] != 0:
                self.coef_ = self.coef_ * self.y_std_[0]

        return self

    def transform(self, X):
        """
        Apply the dimension reduction.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        x_scores : array
            X scores (predictive and orthogonal).
        """
        check_is_fitted(self, 'w_')
        X = check_array(X, copy=self.copy)

        # Center X
        X_centered = X - self.x_mean_

        # Scale X if requested
        if self.scale:
            X_centered = X_centered / self.x_std_

        # Calculate predictive score
        t = X_centered @ self.w_

        # Calculate orthogonal scores
        t_ortho = X_centered @ self.W_ortho_

        # Combine scores
        scores = np.column_stack([t.reshape(-1, 1), t_ortho])

        return scores

    def predict(self, X):
        """
        Predict using the model.

        Parameters:
        -----------
        X : array-like
            Training vectors, where n_samples is the number of samples and
            n_features is the number of predictors.

        Returns:
        --------
        Y_pred : array
            Predicted values.
        """
        check_is_fitted(self, 'coef_')
        X = check_array(X, copy=self.copy)

        # Center X
        X_centered = X - self.x_mean_

        # Scale X if requested
        if self.scale:
            X_centered = X_centered / self.x_std_

        # Predict
        Y_pred = X_centered @ self.coef_ + self.y_mean_

        return Y_pred
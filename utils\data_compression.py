import numpy as np
import pandas as pd
from sklearn.decomposition import PCA, FastICA
from scipy.fftpack import dct, idct
import pywt

def apply_pca(X, n_components=None, variance_threshold=0.95):
    """
    Apply Principal Component Analysis (PCA) for data compression.
    
    Parameters:
    -----------
    X : array-like
        Input data to be compressed
    n_components : int, optional
        Number of components to keep. If None, determined by variance_threshold.
    variance_threshold : float
        Minimum cumulative explained variance to determine number of components
        
    Returns:
    --------
    X_compressed : array-like
        Compressed data
    pca : object
        Fitted PCA model
    """
    # Convert to numpy array if it's a pandas DataFrame
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        is_dataframe = True
        X_array = X.values
    else:
        is_dataframe = False
        X_array = X
    
    # Determine number of components if not provided
    if n_components is None:
        # Initialize PCA with all components
        pca_full = PCA()
        pca_full.fit(X_array)
        
        # Calculate cumulative explained variance
        cumulative_variance = np.cumsum(pca_full.explained_variance_ratio_)
        
        # Find number of components that explain at least variance_threshold of variance
        n_components = np.argmax(cumulative_variance >= variance_threshold) + 1
    
    # Apply PCA with determined number of components
    pca = PCA(n_components=n_components)
    X_transformed = pca.fit_transform(X_array)
    
    # Reconstruct data
    X_compressed = pca.inverse_transform(X_transformed)
    
    # Convert back to DataFrame if input was DataFrame
    if is_dataframe:
        X_compressed = pd.DataFrame(X_compressed, index=index, columns=columns)
    
    return X_compressed, pca

def apply_ica(X, n_components=None):
    """
    Apply Independent Component Analysis (ICA) for data compression.
    
    Parameters:
    -----------
    X : array-like
        Input data to be compressed
    n_components : int, optional
        Number of components to keep. If None, use min(n_samples, n_features).
        
    Returns:
    --------
    X_compressed : array-like
        Compressed data
    ica : object
        Fitted ICA model
    """
    # Convert to numpy array if it's a pandas DataFrame
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        is_dataframe = True
        X_array = X.values
    else:
        is_dataframe = False
        X_array = X
    
    # Determine number of components if not provided
    if n_components is None:
        n_components = min(X_array.shape)
    
    # Apply ICA
    ica = FastICA(n_components=n_components, random_state=42)
    X_transformed = ica.fit_transform(X_array)
    
    # Reconstruct data
    X_compressed = ica.inverse_transform(X_transformed)
    
    # Convert back to DataFrame if input was DataFrame
    if is_dataframe:
        X_compressed = pd.DataFrame(X_compressed, index=index, columns=columns)
    
    return X_compressed, ica

def apply_wavelet_transform(X, wavelet='db4', level=None, threshold=None, mode='soft'):
    """
    Apply Wavelet Transform for data compression.
    
    Parameters:
    -----------
    X : array-like
        Input data to be compressed
    wavelet : str
        Wavelet to use (e.g., 'db4', 'sym8', 'haar')
    level : int, optional
        Decomposition level. If None, calculated based on data length.
    threshold : float, optional
        Threshold for coefficient thresholding. If None, no thresholding is applied.
    mode : str
        Thresholding mode ('soft' or 'hard')
        
    Returns:
    --------
    X_compressed : array-like
        Compressed data
    wavelet_info : dict
        Information about the wavelet transform
    """
    # Convert to numpy array if it's a pandas DataFrame
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        is_dataframe = True
        X_array = X.values
    else:
        is_dataframe = False
        X_array = X
    
    # Initialize array to store compressed data
    X_compressed = np.zeros_like(X_array)
    
    # Apply wavelet transform to each row
    for i in range(X_array.shape[0]):
        # Get current row
        x = X_array[i, :]
        
        # Determine decomposition level if not provided
        if level is None:
            level = pywt.dwt_max_level(len(x), wavelet)
        
        # Apply wavelet decomposition
        coeffs = pywt.wavedec(x, wavelet, level=level)
        
        # Apply thresholding if threshold is provided
        if threshold is not None:
            coeffs_thresholded = []
            for j, coeff in enumerate(coeffs):
                if mode == 'soft':
                    # Soft thresholding
                    coeff_thresh = pywt.threshold(coeff, threshold, mode='soft')
                else:
                    # Hard thresholding
                    coeff_thresh = pywt.threshold(coeff, threshold, mode='hard')
                coeffs_thresholded.append(coeff_thresh)
            coeffs = coeffs_thresholded
        
        # Reconstruct signal
        X_compressed[i, :] = pywt.waverec(coeffs, wavelet)
    
    # Convert back to DataFrame if input was DataFrame
    if is_dataframe:
        X_compressed = pd.DataFrame(X_compressed, index=index, columns=columns)
    
    # Prepare wavelet information
    wavelet_info = {
        'wavelet': wavelet,
        'level': level,
        'threshold': threshold,
        'mode': mode
    }
    
    return X_compressed, wavelet_info

def apply_fourier_transform(X, n_components=None):
    """
    Apply Fourier Transform for data compression.
    
    Parameters:
    -----------
    X : array-like
        Input data to be compressed
    n_components : int, optional
        Number of frequency components to keep. If None, keep all components.
        
    Returns:
    --------
    X_compressed : array-like
        Compressed data
    fourier_info : dict
        Information about the Fourier transform
    """
    # Convert to numpy array if it's a pandas DataFrame
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        is_dataframe = True
        X_array = X.values
    else:
        is_dataframe = False
        X_array = X
    
    # Initialize array to store compressed data
    X_compressed = np.zeros_like(X_array)
    
    # Apply Fourier transform to each row
    for i in range(X_array.shape[0]):
        # Get current row
        x = X_array[i, :]
        
        # Apply Fourier transform
        x_fft = np.fft.rfft(x)
        
        # Keep only n_components if specified
        if n_components is not None:
            # Ensure n_components is not larger than the number of frequencies
            n_components = min(n_components, len(x_fft))
            
            # Keep only the first n_components frequencies
            mask = np.zeros_like(x_fft, dtype=bool)
            mask[:n_components] = True
            
            # Apply mask
            x_fft_filtered = x_fft.copy()
            x_fft_filtered[~mask] = 0
            
            # Inverse Fourier transform
            X_compressed[i, :] = np.fft.irfft(x_fft_filtered, len(x))
        else:
            # No compression, just transform and inverse transform
            X_compressed[i, :] = np.fft.irfft(x_fft, len(x))
    
    # Convert back to DataFrame if input was DataFrame
    if is_dataframe:
        X_compressed = pd.DataFrame(X_compressed, index=index, columns=columns)
    
    # Prepare Fourier information
    fourier_info = {
        'n_components': n_components
    }
    
    return X_compressed, fourier_info

def apply_dct(X, n_components=None):
    """
    Apply Discrete Cosine Transform (DCT) for data compression.
    
    Parameters:
    -----------
    X : array-like
        Input data to be compressed
    n_components : int, optional
        Number of DCT coefficients to keep. If None, keep all coefficients.
        
    Returns:
    --------
    X_compressed : array-like
        Compressed data
    dct_info : dict
        Information about the DCT
    """
    # Convert to numpy array if it's a pandas DataFrame
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        is_dataframe = True
        X_array = X.values
    else:
        is_dataframe = False
        X_array = X
    
    # Initialize array to store compressed data
    X_compressed = np.zeros_like(X_array)
    
    # Apply DCT to each row
    for i in range(X_array.shape[0]):
        # Get current row
        x = X_array[i, :]
        
        # Apply DCT
        x_dct = dct(x, type=2, norm='ortho')
        
        # Keep only n_components if specified
        if n_components is not None:
            # Ensure n_components is not larger than the number of coefficients
            n_components = min(n_components, len(x_dct))
            
            # Keep only the first n_components coefficients
            x_dct_filtered = np.zeros_like(x_dct)
            x_dct_filtered[:n_components] = x_dct[:n_components]
            
            # Inverse DCT
            X_compressed[i, :] = idct(x_dct_filtered, type=2, norm='ortho')
        else:
            # No compression, just transform and inverse transform
            X_compressed[i, :] = idct(x_dct, type=2, norm='ortho')
    
    # Convert back to DataFrame if input was DataFrame
    if is_dataframe:
        X_compressed = pd.DataFrame(X_compressed, index=index, columns=columns)
    
    # Prepare DCT information
    dct_info = {
        'n_components': n_components
    }
    
    return X_compressed, dct_info

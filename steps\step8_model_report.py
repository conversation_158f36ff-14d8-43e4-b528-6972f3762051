"""
Step 8: Developed Model Report for MELK Chemo Copilot

This step generates a comprehensive report of the entire model development process.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any, List, Tuple, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import Chat<PERSON>THelper
from datetime import datetime
import base64
from io import BytesIO
import warnings
warnings.filterwarnings('ignore')


class Step8ModelReport(BaseStep):
    """Step 8: Comprehensive Model Development Report."""

    def __init__(self):
        super().__init__(step_number=8, step_name="Developed Model Report")

    def render(self) -> None:
        """Render the comprehensive model report."""
        # Header with help icon
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("## 📊 Step 8: Developed Model Report")
            st.markdown("*Comprehensive report of your complete chemometric model development workflow*")
        with col2:
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Model Development Report",
                "understanding comprehensive chemometric model reports",
                """Please explain the importance of comprehensive model development reports in chemometrics:

1. What should be included in a complete chemometric model report?
2. How to document model development workflow and decisions?
3. What visualizations and metrics are essential for model validation?
4. How to ensure reproducibility and traceability of results?
5. What are best practices for chemometric model documentation?

Please provide guidance for creating professional chemometric model reports."""
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Check prerequisites
        if not self._check_prerequisites():
            return

        # Generate and display report
        self._render_comprehensive_report()

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        if not self.session.has("built_model"):
            missing_items.append("Built model from Step 6")
        if not self.session.has("final_model_config"):
            missing_items.append("Model configuration from Step 5")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 7", key="back_to_step7"):
                self.session.set_current_step(7)
                st.rerun()
            return False

        return True

    def _render_comprehensive_report(self) -> None:
        """Render the comprehensive model development report."""
        st.markdown("### 📋 Comprehensive Model Development Report")
        st.markdown(f"*Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")

        # Report sections
        self._render_executive_summary()
        self._render_data_overview_section()
        self._render_preprocessing_section()
        self._render_variable_selection_section()
        self._render_model_selection_section()
        self._render_model_building_section()
        self._render_prediction_results_section()
        self._render_conclusions_section()

        # Download report
        self._render_download_section()

    def _render_executive_summary(self) -> None:
        """Render executive summary section."""
        st.markdown("#### 🎯 Executive Summary")

        # Get key information
        built_model = self.session.get("built_model")
        final_config = self.session.get("final_model_config")

        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Algorithm", built_model["algorithm"])
        with col2:
            st.metric("Final CV RMSE", f"{built_model['cv_rmse']:.4f}")
        with col3:
            st.metric("Final CV R²", f"{built_model['cv_r2']:.4f}")
        with col4:
            training_samples = built_model['training_data_shape'][0]
            st.metric("Training Samples", training_samples)

        # Summary text
        st.markdown(f"""
        **Model Development Summary:**

        A {built_model['algorithm']} model was successfully developed using a {training_samples}-sample training dataset.
        The model achieved a cross-validation RMSE of {built_model['cv_rmse']:.4f} and R² of {built_model['cv_r2']:.4f},
        indicating {'excellent' if built_model['cv_r2'] > 0.9 else 'good' if built_model['cv_r2'] > 0.8 else 'moderate'}
        predictive performance.

        The model development followed a systematic workflow including data preprocessing, variable selection,
        model selection with cross-validation, and final model building and validation.
        """)

    def _render_data_overview_section(self) -> None:
        """Render data overview section."""
        with st.expander("📊 Data Overview", expanded=False):
            st.markdown("#### Data Overview")

            # Training data info
            if self.session.has("x_train"):
                x_train = self.session.get("x_train")
                y_train = self.session.get("y_train")

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Training Samples", len(x_train))
                with col2:
                    st.metric("Original Features", x_train.shape[1])
                with col3:
                    st.metric("Response Variables", y_train.shape[1] if len(y_train.shape) > 1 else 1)

            # Test data info
            if self.session.has("x_test"):
                x_test = self.session.get("x_test")
                st.metric("Test Samples", len(x_test))

            st.markdown("**Data Quality:** All data passed initial validation checks.")

    def _render_preprocessing_section(self) -> None:
        """Render preprocessing section."""
        with st.expander("🔧 Preprocessing", expanded=False):
            st.markdown("#### Preprocessing")

            if self.session.has("preprocessing_results"):
                preprocessing_results = self.session.get("preprocessing_results")

                st.markdown(f"**Selected Method:** {preprocessing_results['best_method']}")
                st.markdown(f"**Optimal Components:** {preprocessing_results['best_components']}")
                st.markdown(f"**Best RMSECV:** {preprocessing_results['best_rmsecv']:.4f}")

                # Show preprocessing comparison table
                if 'results_df' in preprocessing_results:
                    st.markdown("**Method Comparison:**")
                    st.dataframe(preprocessing_results['results_df'])
            else:
                st.info("Preprocessing results not available.")

    def _render_variable_selection_section(self) -> None:
        """Render variable selection section."""
        with st.expander("🎯 Variable Selection", expanded=False):
            st.markdown("#### Variable Selection")

            if self.session.has("variable_selection_results"):
                var_results = self.session.get("variable_selection_results")

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Method", var_results.get('method', 'N/A'))
                with col2:
                    original_vars = self.session.get("x_train").shape[1] if self.session.has("x_train") else 0
                    selected_vars = len(var_results.get('selected_variables', []))
                    st.metric("Variables Selected", f"{selected_vars}/{original_vars}")
                with col3:
                    if 'improvement_rmsecv' in var_results:
                        st.metric("RMSECV Improvement", f"{var_results['improvement_rmsecv']:.4f}")
            else:
                st.info("Variable selection results not available.")

    def _render_model_selection_section(self) -> None:
        """Render model selection section."""
        with st.expander("🤖 Model Selection & Cross-Validation", expanded=False):
            st.markdown("#### Model Selection & Cross-Validation")

            final_config = self.session.get("final_model_config")

            # Configuration details
            st.markdown("**Configuration:**")
            col1, col2 = st.columns(2)
            with col1:
                st.write(f"• **Algorithm:** {final_config['algorithm']}")
                route = "Manual" if final_config['optimization_route'] == 'manual' else "Automated Grid Search"
                st.write(f"• **Optimization Route:** {route}")
            with col2:
                if final_config['optimization_route'] == 'manual':
                    st.write(f"• **RMSECV:** {final_config['results']['rmsecv']:.4f}")
                    st.write(f"• **R²:** {final_config['results']['r2']:.4f}")
                else:
                    st.write(f"• **Best RMSECV:** {final_config['results']['best_rmsecv']:.4f}")
                    st.write(f"• **Best R²:** {final_config['results']['best_r2']:.4f}")

            # Cross-validation details
            if self.session.has("cv_configuration"):
                cv_config = self.session.get("cv_configuration")
                st.markdown("**Cross-Validation Configuration:**")
                st.write(f"• **Method:** {cv_config['method']}")
                st.write(f"• **Parameters:** {cv_config['params']}")
                if cv_config['mode'] == 'automatic':
                    st.write(f"• **Reasoning:** {cv_config['reasoning']}")

    def _render_model_building_section(self) -> None:
        """Render model building section."""
        with st.expander("🏗️ Model Building", expanded=False):
            st.markdown("#### Model Building")

            built_model = self.session.get("built_model")

            # Model performance
            st.markdown("**Final Model Performance:**")
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Training RMSE", f"{built_model['train_rmse']:.4f}")
            with col2:
                st.metric("Training R²", f"{built_model['train_r2']:.4f}")
            with col3:
                st.metric("CV RMSE", f"{built_model['cv_rmse']:.4f}")
            with col4:
                st.metric("CV R²", f"{built_model['cv_r2']:.4f}")

            # Model parameters
            st.markdown("**Model Parameters:**")
            st.json(built_model['parameters'])

    def _render_prediction_results_section(self) -> None:
        """Render prediction results section."""
        with st.expander("🎯 Prediction Results", expanded=False):
            st.markdown("#### Prediction Results")

            # Test data predictions
            if self.session.has("test_predictions"):
                test_results = self.session.get("test_predictions")

                st.markdown("**Test Data Validation:**")
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Test RMSE", f"{test_results['test_rmse']:.4f}")
                with col2:
                    st.metric("Test R²", f"{test_results['test_r2']:.4f}")
                with col3:
                    st.metric("Test Samples", len(test_results['y_true']))

                # Create prediction plot
                self._create_summary_prediction_plot(test_results)
            else:
                st.info("No test data predictions available.")

    def _create_summary_prediction_plot(self, test_results: Dict[str, Any]) -> None:
        """Create summary prediction plot for report."""
        y_true = test_results['y_true']
        y_pred = test_results['y_pred']

        fig = go.Figure()

        # Scatter plot
        fig.add_trace(go.Scatter(
            x=y_true,
            y=y_pred,
            mode='markers',
            name='Test Predictions',
            marker=dict(size=8, color='rgba(25, 118, 210, 0.7)')
        ))

        # Perfect prediction line
        min_val = min(min(y_true), min(y_pred))
        max_val = max(max(y_true), max(y_pred))
        fig.add_trace(go.Scatter(
            x=[min_val, max_val],
            y=[min_val, max_val],
            mode='lines',
            name='Perfect Prediction',
            line=dict(color='red', dash='dash', width=2)
        ))

        fig.update_layout(
            title=f"Test Data: Predicted vs Actual (R² = {test_results['test_r2']:.3f})",
            xaxis_title="Actual Values",
            yaxis_title="Predicted Values",
            font=dict(family="Nunito Sans", size=12),
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

    def _render_conclusions_section(self) -> None:
        """Render conclusions section."""
        with st.expander("📝 Conclusions & Recommendations", expanded=True):
            st.markdown("#### Conclusions & Recommendations")

            built_model = self.session.get("built_model")
            cv_r2 = built_model['cv_r2']
            cv_rmse = built_model['cv_rmse']

            # Performance assessment
            if cv_r2 > 0.9:
                performance = "excellent"
                recommendation = "The model shows excellent predictive performance and is ready for production use."
            elif cv_r2 > 0.8:
                performance = "good"
                recommendation = "The model shows good predictive performance and can be used with confidence."
            elif cv_r2 > 0.7:
                performance = "moderate"
                recommendation = "The model shows moderate performance. Consider additional data or feature engineering."
            else:
                performance = "limited"
                recommendation = "The model shows limited performance. Review data quality and modeling approach."

            st.markdown(f"""
            **Model Performance Assessment:**
            - The developed {built_model['algorithm']} model demonstrates **{performance}** predictive performance
            - Cross-validation R² of {cv_r2:.3f} indicates {performance} model fit
            - Cross-validation RMSE of {cv_rmse:.4f} shows prediction accuracy

            **Recommendations:**
            - {recommendation}
            - Regular model validation with new data is recommended
            - Monitor model performance over time for potential drift
            - Document any changes to input data preprocessing
            """)

    def _render_download_section(self) -> None:
        """Render download section."""
        st.markdown("---")
        st.markdown("### 📥 Download Report")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 Download Summary Report", key="download_summary"):
                self._generate_summary_report()

        with col2:
            if st.button("📋 Download Detailed Report", key="download_detailed"):
                self._generate_detailed_report()

        with col3:
            if st.button("🔄 Restart Workflow", key="restart_workflow"):
                if st.confirm("Are you sure you want to restart the entire workflow? All progress will be lost."):
                    # Clear all session data
                    for key in list(st.session_state.keys()):
                        if not key.startswith('_'):
                            del st.session_state[key]
                    st.success("Workflow restarted!")
                    st.rerun()

    def _generate_summary_report(self) -> None:
        """Generate and download summary report."""
        # Create summary data
        built_model = self.session.get("built_model")
        final_config = self.session.get("final_model_config")

        summary_data = {
            "Model Development Report": [
                f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"Algorithm: {built_model['algorithm']}",
                f"Training Samples: {built_model['training_data_shape'][0]}",
                f"Features: {built_model['training_data_shape'][1]}",
                f"CV RMSE: {built_model['cv_rmse']:.4f}",
                f"CV R²: {built_model['cv_r2']:.4f}",
                f"Optimization Route: {'Manual' if final_config['optimization_route'] == 'manual' else 'Automated'}"
            ]
        }

        summary_df = pd.DataFrame(summary_data)
        csv = summary_df.to_csv(index=False)

        st.download_button(
            label="📥 Download Summary CSV",
            data=csv,
            file_name=f"model_summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    def _generate_detailed_report(self) -> None:
        """Generate and download detailed report."""
        st.info("Detailed report generation feature coming soon!")

    def _render_final_navigation(self) -> None:
        """Render final navigation."""
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Step 7", key="back_to_step7_final"):
                self.session.set_current_step(7)
                st.rerun()

        with col3:
            st.success("🎉 Workflow Complete!")
            if st.button("🏠 Return to Home", key="return_home"):
                self.session.set_current_step(1)
                st.rerun()

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from scipy.signal import savgol_filter

def mean_center_data(X):
    """
    Apply mean centering to the data.
    
    Parameters:
    -----------
    X : array-like
        Input data to be mean-centered
        
    Returns:
    --------
    X_centered : array-like
        Mean-centered data
    """
    scaler = StandardScaler(with_mean=True, with_std=False)
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        
        # Apply mean centering
        X_centered = scaler.fit_transform(X)
        
        # Convert back to DataFrame
        X_centered = pd.DataFrame(X_centered, index=index, columns=columns)
    else:
        X_centered = scaler.fit_transform(X)
    
    return X_centered

def autoscale_data(X):
    """
    Apply autoscaling (standardization) to the data.
    
    Parameters:
    -----------
    X : array-like
        Input data to be autoscaled
        
    Returns:
    --------
    X_scaled : array-like
        Autoscaled data
    """
    scaler = StandardScaler(with_mean=True, with_std=True)
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        
        # Apply autoscaling
        X_scaled = scaler.fit_transform(X)
        
        # Convert back to DataFrame
        X_scaled = pd.DataFrame(X_scaled, index=index, columns=columns)
    else:
        X_scaled = scaler.fit_transform(X)
    
    return X_scaled

def apply_snv(X):
    """
    Apply Standard Normal Variate (SNV) transformation to the data.
    
    Parameters:
    -----------
    X : array-like
        Input data to be transformed
        
    Returns:
    --------
    X_snv : array-like
        SNV transformed data
    """
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        
        # Convert to numpy array for processing
        X_array = X.values
        
        # Apply SNV
        X_snv = np.zeros_like(X_array)
        for i in range(X_array.shape[0]):
            X_snv[i, :] = (X_array[i, :] - np.mean(X_array[i, :])) / np.std(X_array[i, :])
        
        # Convert back to DataFrame
        X_snv = pd.DataFrame(X_snv, index=index, columns=columns)
    else:
        X_snv = np.zeros_like(X)
        for i in range(X.shape[0]):
            X_snv[i, :] = (X[i, :] - np.mean(X[i, :])) / np.std(X[i, :])
    
    return X_snv

def apply_savgol(X, window=11, polyorder=2, deriv=1):
    """
    Apply Savitzky-Golay filter to the data.
    
    Parameters:
    -----------
    X : array-like
        Input data to be filtered
    window : int
        Window size (must be odd)
    polyorder : int
        Polynomial order
    deriv : int
        Derivative order
        
    Returns:
    --------
    X_savgol : array-like
        Savitzky-Golay filtered data
    """
    if isinstance(X, pd.DataFrame):
        # Save column names and index
        columns = X.columns
        index = X.index
        
        # Convert to numpy array for processing
        X_array = X.values
        
        # Apply Savitzky-Golay filter
        X_savgol = np.zeros_like(X_array)
        for i in range(X_array.shape[0]):
            X_savgol[i, :] = savgol_filter(X_array[i, :], window, polyorder, deriv=deriv)
        
        # Convert back to DataFrame
        X_savgol = pd.DataFrame(X_savgol, index=index, columns=columns)
    else:
        X_savgol = np.zeros_like(X)
        for i in range(X.shape[0]):
            X_savgol[i, :] = savgol_filter(X[i, :], window, polyorder, deriv=deriv)
    
    return X_savgol

def apply_snv_and_savgol(X, window=11, polyorder=2, deriv=1):
    """
    Apply SNV followed by Savitzky-Golay filter to the data.
    
    Parameters:
    -----------
    X : array-like
        Input data to be transformed
    window : int
        Window size for Savitzky-Golay filter (must be odd)
    polyorder : int
        Polynomial order for Savitzky-Golay filter
    deriv : int
        Derivative order for Savitzky-Golay filter
        
    Returns:
    --------
    X_transformed : array-like
        Transformed data
    """
    # First apply SNV
    X_snv = apply_snv(X)
    
    # Then apply Savitzky-Golay filter
    X_transformed = apply_savgol(X_snv, window, polyorder, deriv)
    
    return X_transformed

def preprocess_data(X, method):
    """
    Apply the specified preprocessing method to the data.
    
    Parameters:
    -----------
    X : array-like
        Input data to be preprocessed
    method : str
        Preprocessing method to apply
        
    Returns:
    --------
    X_preprocessed : array-like
        Preprocessed data
    """
    if method == 'mean_centering':
        return mean_center_data(X)
    elif method == 'autoscaling':
        return autoscale_data(X)
    elif method == 'snv':
        return apply_snv(X)
    elif method == 'savgol':
        return apply_savgol(X)
    elif method == 'snv_savgol':
        return apply_snv_and_savgol(X)
    else:
        raise ValueError(f"Unknown preprocessing method: {method}")

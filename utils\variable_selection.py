import numpy as np
import pandas as pd
from sklearn.cross_decomposition import PLSRegression
from sklearn.model_selection import <PERSON><PERSON><PERSON>
from sklearn.metrics import mean_squared_error
import random
from .model import calculate_vip_scores, perform_pls_cv

def apply_vip_selection(X, Y, pls_model, threshold=1.0):
    """
    Apply variable selection based on VIP scores.
    
    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    pls_model : PLSRegression
        Fitted PLS model
    threshold : float
        VIP score threshold for variable selection
        
    Returns:
    --------
    selected_vars : array
        Boolean mask of selected variables
    vip_scores : array
        VIP scores for each variable
    """
    # Calculate VIP scores
    vip_scores = calculate_vip_scores(pls_model, X)
    
    # Select variables with VIP scores above threshold
    selected_vars = vip_scores >= threshold
    
    return selected_vars, vip_scores

def apply_genetic_algorithm(X, Y, n_components, population_size=30, generations=40, 
                           crossover_prob=0.8, mutation_prob=0.1, n_splits=10, random_state=42):
    """
    Apply genetic algorithm for variable selection in PLS.
    
    Parameters:
    -----------
    X : array-like
        Predictor variables
    Y : array-like
        Response variables
    n_components : int
        Number of PLS components
    population_size : int
        Size of the population
    generations : int
        Number of generations
    crossover_prob : float
        Probability of crossover
    mutation_prob : float
        Probability of mutation
    n_splits : int
        Number of folds for cross-validation
    random_state : int
        Random seed for reproducibility
        
    Returns:
    --------
    best_solution : array
        Boolean mask of selected variables
    ga_history : dict
        History of the genetic algorithm optimization
    """
    # Set random seed
    np.random.seed(random_state)
    random.seed(random_state)
    
    # Convert to numpy arrays if they are pandas DataFrames
    if isinstance(X, pd.DataFrame):
        X = X.values
    if isinstance(Y, pd.DataFrame):
        Y = Y.values
    
    # Ensure Y is 2D
    if Y.ndim == 1:
        Y = Y.reshape(-1, 1)
    
    n_samples, n_variables = X.shape
    
    # Initialize k-fold cross-validation
    kf = KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
    
    # Define fitness function (minimize RMSECV)
    def fitness_function(solution):
        # If no variables are selected, return a high error
        if np.sum(solution) == 0:
            return 999999.0
        
        # Select variables
        X_selected = X[:, solution]
        
        # Initialize array to store CV errors
        cv_errors = np.zeros(n_splits)
        
        # Perform cross-validation
        for fold_idx, (train_idx, test_idx) in enumerate(kf.split(X_selected)):
            X_train, X_test = X_selected[train_idx], X_selected[test_idx]
            Y_train, Y_test = Y[train_idx], Y[test_idx]
            
            # Fit PLS model
            pls = PLSRegression(n_components=min(n_components, X_train.shape[1]))
            pls.fit(X_train, Y_train)
            
            # Predict test set
            Y_pred = pls.predict(X_test)
            
            # Calculate RMSE
            cv_errors[fold_idx] = np.sqrt(mean_squared_error(Y_test, Y_pred))
        
        # Return mean RMSECV
        return np.mean(cv_errors)
    
    # Initialize population
    population = []
    for _ in range(population_size):
        # Create random solution (binary mask)
        solution = np.random.random(n_variables) < 0.5
        # Ensure at least one variable is selected
        if np.sum(solution) == 0:
            solution[np.random.randint(0, n_variables)] = True
        population.append(solution)
    
    # Initialize history
    history = {
        'best_fitness': [],
        'avg_fitness': [],
        'best_solution': None
    }
    
    # Run genetic algorithm
    for generation in range(generations):
        # Evaluate fitness for each solution
        fitness_values = [fitness_function(solution) for solution in population]
        
        # Find best solution
        best_idx = np.argmin(fitness_values)
        best_solution = population[best_idx].copy()
        best_fitness = fitness_values[best_idx]
        
        # Update history
        history['best_fitness'].append(best_fitness)
        history['avg_fitness'].append(np.mean(fitness_values))
        history['best_solution'] = best_solution
        
        # Create new population
        new_population = []
        
        # Elitism: keep the best solution
        new_population.append(best_solution)
        
        # Tournament selection and crossover
        while len(new_population) < population_size:
            # Tournament selection
            tournament_size = 3
            idx1 = min(random.sample(range(population_size), tournament_size), 
                       key=lambda i: fitness_values[i])
            idx2 = min(random.sample(range(population_size), tournament_size), 
                       key=lambda i: fitness_values[i])
            
            parent1 = population[idx1]
            parent2 = population[idx2]
            
            # Crossover
            if random.random() < crossover_prob:
                # Single-point crossover
                crossover_point = random.randint(1, n_variables - 1)
                child1 = np.concatenate([parent1[:crossover_point], parent2[crossover_point:]])
                child2 = np.concatenate([parent2[:crossover_point], parent1[crossover_point:]])
            else:
                child1 = parent1.copy()
                child2 = parent2.copy()
            
            # Mutation
            for child in [child1, child2]:
                for i in range(n_variables):
                    if random.random() < mutation_prob:
                        child[i] = not child[i]
                
                # Ensure at least one variable is selected
                if np.sum(child) == 0:
                    child[np.random.randint(0, n_variables)] = True
                
                new_population.append(child)
                
                # Check if we have enough solutions
                if len(new_population) >= population_size:
                    break
        
        # Replace old population with new population
        population = new_population[:population_size]
    
    return history['best_solution'], history

"""
Streamlined Enhanced ANN Implementation for MELK Chemo Copilot
Integrates with existing preprocessing workflow (Steps 3 & 4) to avoid redundancy.

Features:
- MATLAB-style neural network architecture and training
- Flexible data division strategies
- Multiple training algorithms and activation functions
- Comprehensive evaluation metrics and diagnostics
- Integration with existing MELK preprocessing pipeline
"""

import numpy as np
import warnings
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from typing import Optional, Tuple, Dict, Any, List, Union
import pandas as pd


class EnhancedANN(BaseEstimator, RegressorMixin):
    """
    Enhanced Artificial Neural Network for chemometric applications.

    Integrates with MELK preprocessing pipeline while providing:
    - MATLAB-style neural network configurations
    - Flexible data division strategies
    - Multiple training algorithms and architectures
    - Comprehensive evaluation and diagnostics

    Note: Preprocessing (normalization, PCA) is handled by Steps 3 & 4
    """

    def __init__(self,
                 hidden_layer_sizes: Tuple[int, ...] = (20,),
                 activation: str = 'tanh',
                 solver: str = 'lbfgs',
                 alpha: float = 0.0001,
                 learning_rate_init: float = 0.001,
                 max_iter: int = 500,
                 random_state: int = 42,
                 # Enhanced parameters for data division
                 data_division: str = 'random',
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 test_ratio: float = 0.15,
                 early_stopping: bool = True,
                 validation_fraction: float = 0.1):

        # Standard MLPRegressor parameters
        self.hidden_layer_sizes = hidden_layer_sizes
        self.activation = activation
        self.solver = solver
        self.alpha = alpha
        self.learning_rate_init = learning_rate_init
        self.max_iter = max_iter
        self.random_state = random_state

        # Enhanced parameters for data division
        self.data_division = data_division
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.early_stopping = early_stopping
        self.validation_fraction = validation_fraction

        # Initialize components
        self.model_ = None
        self.training_history_ = {}
        self.data_division_indices_ = {}

    def _validate_data_division_ratios(self):
        """Validate that data division ratios sum to 1.0."""
        total = self.train_ratio + self.val_ratio + self.test_ratio
        if not np.isclose(total, 1.0, atol=1e-6):
            raise ValueError(f"Data division ratios must sum to 1.0, got {total}")

    def _divide_data(self, X: np.ndarray, y: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Divide data into train/validation/test sets.

        Parameters
        ----------
        X : array-like
            Input features
        y : array-like
            Target values

        Returns
        -------
        data_dict : dictionary with train/val/test splits
        """
        n_samples = X.shape[0]

        if self.data_division == 'random':
            # Random division
            indices = np.random.RandomState(self.random_state).permutation(n_samples)
        elif self.data_division == 'sequential':
            # Sequential division (like MATLAB divideblock)
            indices = np.arange(n_samples)
        elif self.data_division == 'interleaved':
            # Interleaved division
            indices = np.arange(n_samples)
            np.random.RandomState(self.random_state).shuffle(indices)
        else:
            raise ValueError(f"Unknown data_division method: {self.data_division}")

        # Calculate split points
        n_train = int(n_samples * self.train_ratio)
        n_val = int(n_samples * self.val_ratio)

        # Split indices
        train_idx = indices[:n_train]
        val_idx = indices[n_train:n_train + n_val]
        test_idx = indices[n_train + n_val:]

        # Store indices for later use
        self.data_division_indices_ = {
            'train': train_idx,
            'validation': val_idx,
            'test': test_idx
        }

        return {
            'X_train': X[train_idx],
            'y_train': y[train_idx],
            'X_val': X[val_idx],
            'y_val': y[val_idx],
            'X_test': X[test_idx],
            'y_test': y[test_idx]
        }

    def fit(self, X: np.ndarray, y: np.ndarray) -> 'EnhancedANN':
        """
        Fit the enhanced neural network model.

        Note: Assumes X and y are already preprocessed by Steps 3 & 4

        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Preprocessed training data
        y : array-like of shape (n_samples,)
            Target values

        Returns
        -------
        self : object
        """
        # Validate inputs
        X = np.asarray(X)
        y = np.asarray(y)

        if X.ndim != 2:
            raise ValueError("X must be 2-dimensional")
        if y.ndim != 1:
            raise ValueError("y must be 1-dimensional")
        if X.shape[0] != y.shape[0]:
            raise ValueError("X and y must have the same number of samples")

        # Validate data division ratios
        self._validate_data_division_ratios()

        # Divide data into train/validation/test sets
        data_splits = self._divide_data(X, y)

        # Create and configure the neural network (MATLAB-style)
        self.model_ = MLPRegressor(
            hidden_layer_sizes=self.hidden_layer_sizes,
            activation=self.activation,
            solver=self.solver,
            alpha=self.alpha,
            learning_rate_init=self.learning_rate_init,
            max_iter=self.max_iter,
            random_state=self.random_state,
            early_stopping=self.early_stopping,
            validation_fraction=self.validation_fraction if self.early_stopping else 0.1
        )

        # Train the model
        self.model_.fit(data_splits['X_train'], data_splits['y_train'])

        # Store training history and evaluation metrics
        self._evaluate_model(data_splits)

        return self

    def _evaluate_model(self, data_splits: Dict[str, np.ndarray]) -> None:
        """Evaluate model performance on all data splits."""
        self.training_history_ = {}

        for split_name in ['train', 'val', 'test']:
            X_key = f'X_{split_name}'
            y_key = f'y_{split_name}'

            if X_key in data_splits and len(data_splits[X_key]) > 0:
                y_pred = self.model_.predict(data_splits[X_key])
                y_true = data_splits[y_key]

                # Calculate metrics
                mse = mean_squared_error(y_true, y_pred)
                rmse = np.sqrt(mse)
                r2 = r2_score(y_true, y_pred)

                self.training_history_[split_name] = {
                    'mse': mse,
                    'rmse': rmse,
                    'r2': r2,
                    'n_samples': len(y_true)
                }

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Predict using the trained model.

        Note: Assumes X is already preprocessed consistently with training data

        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Preprocessed input samples

        Returns
        -------
        y_pred : array of shape (n_samples,)
            Predicted values
        """
        if self.model_ is None:
            raise ValueError("Model not fitted. Call fit() first.")

        # Predict directly with preprocessed data
        y_pred = self.model_.predict(X)

        return y_pred

    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information."""
        if self.model_ is None:
            raise ValueError("Model not fitted. Call fit() first.")

        info = {
            'algorithm': 'Enhanced ANN (MATLAB-style)',
            'architecture': self.hidden_layer_sizes,
            'activation': self.activation,
            'solver': self.solver,
            'n_iterations': getattr(self.model_, 'n_iter_', 'N/A'),
            'data_division': self.data_division,
            'train_ratio': self.train_ratio,
            'val_ratio': self.val_ratio,
            'test_ratio': self.test_ratio,
            'early_stopping': self.early_stopping,
            'training_history': self.training_history_,
            'data_division_indices': self.data_division_indices_,
            'converged': getattr(self.model_, 'n_iter_', 0) < self.max_iter
        }

        return info

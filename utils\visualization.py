import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from sklearn.metrics import r2_score
import scipy.stats as stats

def plot_rmsecv_vs_lv(rmsecv_values, method_names=None, figsize=(10, 6)):
    """
    Plot RMSECV vs. number of latent variables for different preprocessing methods.

    Parameters:
    -----------
    rmsecv_values : dict or list
        Dictionary with method names as keys and RMSECV arrays as values,
        or list of RMSECV arrays
    method_names : list, optional
        List of method names (if rmsecv_values is a list)
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    if isinstance(rmsecv_values, dict):
        for method, rmsecv in rmsecv_values.items():
            ax.plot(range(1, len(rmsecv) + 1), rmsecv, 'o-', label=method)
    else:
        if method_names is None:
            method_names = [f'Method {i+1}' for i in range(len(rmsecv_values))]

        for i, rmsecv in enumerate(rmsecv_values):
            ax.plot(range(1, len(rmsecv) + 1), rmsecv, 'o-', label=method_names[i])

    ax.set_xlabel('Number of Latent Variables')
    ax.set_ylabel('RMSECV')
    ax.set_title('RMSECV vs. Number of Latent Variables')
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.legend()

    return fig

def plot_rmsecv_vs_lv_plotly(rmsecv_values, method_names=None):
    """
    Create an interactive Plotly plot of RMSECV vs. number of latent variables.

    Parameters:
    -----------
    rmsecv_values : dict or list
        Dictionary with method names as keys and RMSECV arrays as values,
        or list of RMSECV arrays
    method_names : list, optional
        List of method names (if rmsecv_values is a list)

    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure object
    """
    fig = go.Figure()

    if isinstance(rmsecv_values, dict):
        for method, rmsecv in rmsecv_values.items():
            fig.add_trace(go.Scatter(
                x=list(range(1, len(rmsecv) + 1)),
                y=rmsecv,
                mode='lines+markers',
                name=method
            ))
    else:
        if method_names is None:
            method_names = [f'Method {i+1}' for i in range(len(rmsecv_values))]

        for i, rmsecv in enumerate(rmsecv_values):
            fig.add_trace(go.Scatter(
                x=list(range(1, len(rmsecv) + 1)),
                y=rmsecv,
                mode='lines+markers',
                name=method_names[i]
            ))

    fig.update_layout(
        title='RMSECV vs. Number of Latent Variables',
        xaxis_title='Number of Latent Variables',
        yaxis_title='RMSECV',
        legend_title='Preprocessing Method',
        hovermode='closest'
    )

    return fig



def plot_explained_variance(pls_model, figsize=(12, 5)):
    """
    Plot explained variance for X and Y vs. number of latent variables.

    Parameters:
    -----------
    pls_model : PLSRegression
        Fitted PLS model
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

    # Get number of components
    n_components = pls_model.n_components

    # Calculate explained variance for X
    x_scores = pls_model.x_scores_
    x_var = np.var(x_scores, axis=0)
    x_var_ratio = x_var / np.sum(x_var)
    x_var_cumsum = np.cumsum(x_var_ratio)

    # Calculate explained variance for Y
    y_scores = pls_model.y_scores_
    y_var = np.var(y_scores, axis=0)
    y_var_ratio = y_var / np.sum(y_var)
    y_var_cumsum = np.cumsum(y_var_ratio)

    # Plot explained variance for X
    ax1.bar(range(1, n_components + 1), x_var_ratio * 100, alpha=0.7)
    ax1.plot(range(1, n_components + 1), x_var_cumsum * 100, 'ro-', label='Cumulative')
    ax1.set_xlabel('Latent Variable')
    ax1.set_ylabel('Explained Variance (%)')
    ax1.set_title('X Explained Variance')
    ax1.grid(True, linestyle='--', alpha=0.7)
    ax1.legend()

    # Plot explained variance for Y
    ax2.bar(range(1, n_components + 1), y_var_ratio * 100, alpha=0.7)
    ax2.plot(range(1, n_components + 1), y_var_cumsum * 100, 'ro-', label='Cumulative')
    ax2.set_xlabel('Latent Variable')
    ax2.set_ylabel('Explained Variance (%)')
    ax2.set_title('Y Explained Variance')
    ax2.grid(True, linestyle='--', alpha=0.7)
    ax2.legend()

    plt.tight_layout()

    return fig

def plot_predicted_vs_reference(y_true, y_pred, analyte_names=None, figsize=(12, 8)):
    """
    Plot predicted vs. reference values with confidence intervals.

    Parameters:
    -----------
    y_true : array-like
        True response values
    y_pred : array-like
        Predicted response values
    analyte_names : list, optional
        Names of the analytes
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    # Ensure arrays are 2D
    if y_true.ndim == 1:
        y_true = y_true.reshape(-1, 1)
    if y_pred.ndim == 1:
        y_pred = y_pred.reshape(-1, 1)

    n_analytes = y_true.shape[1]

    if analyte_names is None:
        analyte_names = [f'Analyte {i+1}' for i in range(n_analytes)]

    # Calculate number of rows and columns for subplots
    n_cols = min(3, n_analytes)
    n_rows = (n_analytes + n_cols - 1) // n_cols

    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)

    # Flatten axes array if there are multiple subplots
    if n_analytes > 1:
        axes = axes.flatten()
    else:
        axes = [axes]

    for i in range(n_analytes):
        ax = axes[i]

        # Get true and predicted values for the current analyte
        y_true_i = y_true[:, i]
        y_pred_i = y_pred[:, i]

        # Calculate R²
        r2 = r2_score(y_true_i, y_pred_i)

        # Calculate regression line
        slope, intercept, r_value, p_value, std_err = stats.linregress(y_true_i, y_pred_i)

        # Create a range of x values for the regression line
        x_line = np.linspace(min(y_true_i), max(y_true_i), 100)

        # Calculate the regression line
        y_line = intercept + slope * x_line

        # Plot data points
        ax.scatter(y_true_i, y_pred_i, alpha=0.7)

        # Plot regression line
        ax.plot(x_line, y_line, 'r-', label=f'Fit: y = {slope:.3f}x + {intercept:.3f}')

        # Try to calculate and plot prediction intervals
        try:
            # Calculate 95% prediction intervals
            n = len(y_true_i)
            mean_x = np.mean(y_true_i)

            # Calculate standard error of the estimate
            se = np.sqrt(np.sum((y_pred_i - (intercept + slope * y_true_i)) ** 2) / (n - 2))

            # Get t-value for 95% confidence interval
            t_value = stats.t.ppf(0.975, n-2)

            # Calculate prediction interval
            denominator = np.sum((y_true_i - mean_x)**2)
            if denominator > 0:  # Avoid division by zero
                pi = t_value * se * np.sqrt(1 + 1/n + (x_line - mean_x)**2 / denominator)

                # Plot prediction intervals
                ax.fill_between(x_line, y_line - pi, y_line + pi, color='red', alpha=0.2, label='95% PI')
        except Exception as e:
            # If there's an error calculating prediction intervals, just skip them
            print(f"Warning: Could not calculate prediction intervals for analyte {i+1}: {e}")

        # Plot identity line
        ax.plot([min(y_true_i), max(y_true_i)], [min(y_true_i), max(y_true_i)], 'k--', label='y = x')

        ax.set_xlabel('Reference')
        ax.set_ylabel('Predicted')
        ax.set_title(f'{analyte_names[i]} (R² = {r2:.3f})')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.legend()

    # Hide empty subplots
    for i in range(n_analytes, len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()

    return fig

def plot_residuals_vs_leverage(pls_model, X, y_true, y_pred, analyte_names=None, figsize=(12, 8)):
    """
    Plot residuals vs. leverage (Hotelling's T²) to identify outliers.

    Parameters:
    -----------
    pls_model : PLSRegression
        Fitted PLS model
    X : array-like
        Predictor variables
    y_true : array-like
        True response values
    y_pred : array-like
        Predicted response values
    analyte_names : list, optional
        Names of the analytes
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    # Ensure arrays are 2D
    if y_true.ndim == 1:
        y_true = y_true.reshape(-1, 1)
    if y_pred.ndim == 1:
        y_pred = y_pred.reshape(-1, 1)

    n_analytes = y_true.shape[1]

    if analyte_names is None:
        analyte_names = [f'Analyte {i+1}' for i in range(n_analytes)]

    # Calculate number of rows and columns for subplots
    n_cols = min(3, n_analytes)
    n_rows = (n_analytes + n_cols - 1) // n_cols

    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)

    # Flatten axes array if there are multiple subplots
    if n_analytes > 1:
        axes = axes.flatten()
    else:
        axes = [axes]

    try:
        # Calculate leverage (Hotelling's T²)
        scores = pls_model.transform(X)

        # Calculate covariance matrix of scores
        cov_scores = np.cov(scores, rowvar=False)

        # Check if covariance matrix is invertible
        try:
            np.linalg.inv(cov_scores)
            can_calculate_leverage = True
        except np.linalg.LinAlgError:
            can_calculate_leverage = False
            print("Warning: Covariance matrix is singular, cannot calculate leverage.")

        if can_calculate_leverage:
            # Calculate Mahalanobis distance (Hotelling's T²)
            leverage = np.zeros(len(scores))

            for i in range(len(scores)):
                leverage[i] = scores[i] @ np.linalg.inv(cov_scores) @ scores[i].T
        else:
            # Use a simple alternative if covariance matrix is not invertible
            leverage = np.sum(scores**2, axis=1)
    except Exception as e:
        print(f"Warning: Could not calculate leverage: {e}")
        # Use a dummy leverage (just the index) if calculation fails
        leverage = np.arange(len(y_true))

    for i in range(n_analytes):
        ax = axes[i]

        # Calculate residuals
        residuals = y_true[:, i] - y_pred[:, i]

        # Plot residuals vs. leverage
        ax.scatter(leverage, residuals, alpha=0.7)

        try:
            # Calculate critical values
            n_samples = len(residuals)
            p = pls_model.n_components

            # Critical value for leverage (Hotelling's T²)
            if p < n_samples:  # Ensure degrees of freedom are valid
                t2_crit = p * (n_samples - 1) / (n_samples - p) * stats.f.ppf(0.95, p, n_samples - p)
                ax.axvline(t2_crit, color='g', linestyle='--', label='95% Leverage')

            # Critical value for residuals (assuming normal distribution)
            res_crit = stats.norm.ppf(0.975) * np.std(residuals)
            ax.axhline(res_crit, color='r', linestyle='--', label='95% Residual')
            ax.axhline(-res_crit, color='r', linestyle='--')
        except Exception as e:
            print(f"Warning: Could not calculate critical values for analyte {i+1}: {e}")

        ax.set_xlabel('Leverage (Hotelling\'s T²)')
        ax.set_ylabel('Residuals')
        ax.set_title(f'{analyte_names[i]}')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.legend()

    # Hide empty subplots
    for i in range(n_analytes, len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()

    return fig

def plot_vip_scores(vip_scores, variable_names=None, threshold=1.0, figsize=(12, 6)):
    """
    Plot VIP scores for variable selection.

    Parameters:
    -----------
    vip_scores : array-like
        VIP scores for each variable
    variable_names : list, optional
        Names of the variables
    threshold : float
        VIP score threshold for variable selection
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    n_variables = len(vip_scores)

    if variable_names is None:
        variable_names = [f'Var {i+1}' for i in range(n_variables)]

    # Sort VIP scores in descending order
    sorted_idx = np.argsort(vip_scores)[::-1]
    sorted_vip = vip_scores[sorted_idx]
    sorted_names = [variable_names[i] for i in sorted_idx]

    # Plot VIP scores
    bars = ax.bar(range(n_variables), sorted_vip, alpha=0.7)

    # Color bars based on threshold
    for i, bar in enumerate(bars):
        if sorted_vip[i] >= threshold:
            bar.set_color('green')
        else:
            bar.set_color('red')

    # Add threshold line
    ax.axhline(threshold, color='k', linestyle='--', label=f'Threshold = {threshold}')

    ax.set_xlabel('Variable')
    ax.set_ylabel('VIP Score')
    ax.set_title('Variable Importance in Projection (VIP) Scores')
    ax.set_xticks(range(n_variables))

    # If there are too many variables, show only a subset of labels
    if n_variables > 20:
        step = n_variables // 20 + 1
        ax.set_xticks(range(0, n_variables, step))
        ax.set_xticklabels([sorted_names[i] for i in range(0, n_variables, step)], rotation=90)
    else:
        ax.set_xticklabels(sorted_names, rotation=90)

    ax.grid(True, linestyle='--', alpha=0.7)
    ax.legend()

    plt.tight_layout()

    return fig

def plot_interval_selection(intervals, interval_scores, best_interval, variable_names=None, figsize=(12, 6)):
    """
    Plot interval selection results for iPLS.

    Parameters:
    -----------
    intervals : list of arrays
        List of variable indices for each interval
    interval_scores : array-like
        Performance score for each interval
    best_interval : int
        Index of the best interval
    variable_names : list, optional
        Names of variables
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    # Create x-axis values
    x = np.arange(len(intervals))

    # Create bar colors
    colors = ['#1f77b4' if i != best_interval else '#ff7f0e' for i in range(len(intervals))]

    # Plot bars
    ax.bar(x, interval_scores, color=colors)

    # Add labels and title
    ax.set_xlabel('Interval')
    ax.set_ylabel('RMSE')
    ax.set_title('Interval PLS (iPLS) Results')

    # Add x-ticks
    ax.set_xticks(x)
    ax.set_xticklabels([f'Int {i+1}' for i in range(len(intervals))], rotation=90)

    # Highlight best interval
    ax.text(best_interval, interval_scores[best_interval], 'Best',
            ha='center', va='bottom', fontweight='bold')

    # Adjust layout
    plt.tight_layout()

    return fig

def plot_backward_interval_selection(removed_intervals, model_scores, best_model_idx, figsize=(12, 6)):
    """
    Plot backward interval selection results for BiPLS.

    Parameters:
    -----------
    removed_intervals : list
        List of removed intervals at each step
    model_scores : array-like
        Performance score for each model
    best_model_idx : int
        Index of the best model
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    # Create x-axis values
    x = np.arange(len(model_scores))

    # Create bar colors
    colors = ['#1f77b4' if i != best_model_idx else '#ff7f0e' for i in range(len(model_scores))]

    # Plot bars
    ax.bar(x, model_scores, color=colors)

    # Add labels and title
    ax.set_xlabel('Iteration')
    ax.set_ylabel('RMSE')
    ax.set_title('Backward Interval PLS (BiPLS) Results')

    # Add x-ticks
    ax.set_xticks(x)
    ax.set_xticklabels([f'Step {i}' for i in range(len(model_scores))], rotation=90)

    # Add removed interval labels
    for i, interval in enumerate(removed_intervals):
        if i < len(model_scores):
            ax.text(i, model_scores[i], f'Removed: {interval}',
                    ha='center', va='bottom', rotation=90, fontsize=8)

    # Highlight best model
    ax.text(best_model_idx, model_scores[best_model_idx], 'Best',
            ha='center', va='bottom', fontweight='bold')

    # Adjust layout
    plt.tight_layout()

    return fig

def plot_moving_window_selection(window_scores, best_window_idx, window_size, step_size, figsize=(12, 6)):
    """
    Plot moving window selection results for MWPLS.

    Parameters:
    -----------
    window_scores : array-like
        Performance score for each window
    best_window_idx : int
        Index of the best window
    window_size : int
        Size of the window
    step_size : int
        Step size for moving the window
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    # Create x-axis values
    x = np.arange(len(window_scores))

    # Create bar colors
    colors = ['#1f77b4' if i != best_window_idx else '#ff7f0e' for i in range(len(window_scores))]

    # Plot bars
    ax.bar(x, window_scores, color=colors)

    # Add labels and title
    ax.set_xlabel('Window Position')
    ax.set_ylabel('RMSE')
    ax.set_title(f'Moving Window PLS (MWPLS) Results (Window Size: {window_size}, Step Size: {step_size})')

    # Add x-ticks
    if len(window_scores) <= 30:
        ax.set_xticks(x)
        ax.set_xticklabels([f'Pos {i*step_size}' for i in range(len(window_scores))], rotation=90)
    else:
        # Show only some positions
        step = max(1, len(window_scores) // 20)
        ax.set_xticks(x[::step])
        ax.set_xticklabels([f'Pos {i*step_size}' for i in x[::step]], rotation=90)

    # Highlight best window
    ax.text(best_window_idx, window_scores[best_window_idx], 'Best',
            ha='center', va='bottom', fontweight='bold')

    # Adjust layout
    plt.tight_layout()

    return fig

def plot_cars_selection(subset_sizes, subset_scores, best_subset_idx, figsize=(12, 8)):
    """
    Plot CARS selection results.

    Parameters:
    -----------
    subset_sizes : array-like
        Number of variables in each subset
    subset_scores : array-like
        Performance score for each subset
    best_subset_idx : int
        Index of the best subset
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True)

    # Create x-axis values
    x = np.arange(len(subset_scores))

    # Create bar colors
    colors = ['#1f77b4' if i != best_subset_idx else '#ff7f0e' for i in range(len(subset_scores))]

    # Plot RMSE
    ax1.plot(x, subset_scores, 'o-', color='#1f77b4')
    ax1.scatter(best_subset_idx, subset_scores[best_subset_idx], color='#ff7f0e', s=100, zorder=5)
    ax1.set_ylabel('RMSE')
    ax1.set_title('Competitive Adaptive Reweighted Sampling (CARS) Results')

    # Plot number of variables
    ax2.plot(x, subset_sizes, 'o-', color='#2ca02c')
    ax2.scatter(best_subset_idx, subset_sizes[best_subset_idx], color='#ff7f0e', s=100, zorder=5)
    ax2.set_xlabel('Sampling Run')
    ax2.set_ylabel('Number of Variables')

    # Highlight best subset
    ax1.text(best_subset_idx, subset_scores[best_subset_idx], 'Best',
             ha='center', va='bottom', fontweight='bold')

    # Adjust layout
    plt.tight_layout()

    return fig

def plot_uve_selection(reliability_indices, threshold, variable_names=None, figsize=(12, 6)):
    """
    Plot UVE selection results.

    Parameters:
    -----------
    reliability_indices : array-like
        Reliability index for each variable
    threshold : float
        Threshold for variable selection
    variable_names : list, optional
        Names of variables
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    # Create x-axis values
    x = np.arange(len(reliability_indices))

    if variable_names is None:
        variable_names = [f'Var {i+1}' for i in range(len(reliability_indices))]

    # Create bar colors based on threshold
    colors = ['#1f77b4' if idx >= threshold else '#d3d3d3' for idx in reliability_indices]

    # Plot bars
    ax.bar(x, reliability_indices, color=colors)

    # Add threshold line
    ax.axhline(y=threshold, color='r', linestyle='--', label=f'Threshold ({threshold:.4f})')

    # Add labels and title
    ax.set_xlabel('Variables')
    ax.set_ylabel('Reliability Index')
    ax.set_title('Uninformative Variable Elimination (UVE) Results')

    # Add x-ticks with variable names if there are not too many
    if len(variable_names) <= 30:
        ax.set_xticks(x)
        ax.set_xticklabels(variable_names, rotation=90)
    else:
        # Show only some variable names
        step = max(1, len(variable_names) // 20)
        ax.set_xticks(x[::step])
        ax.set_xticklabels([variable_names[i] for i in range(0, len(variable_names), step)], rotation=90)

    # Add legend
    ax.legend()

    # Adjust layout
    plt.tight_layout()

    return fig

def plot_selectivity_ratio(selectivity_ratios, threshold, variable_names=None, figsize=(12, 6)):
    """
    Plot Selectivity Ratio results.

    Parameters:
    -----------
    selectivity_ratios : array-like
        Selectivity ratio for each variable
    threshold : float
        Threshold for variable selection
    variable_names : list, optional
        Names of variables
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    fig, ax = plt.subplots(figsize=figsize)

    # Create x-axis values
    x = np.arange(len(selectivity_ratios))

    if variable_names is None:
        variable_names = [f'Var {i+1}' for i in range(len(selectivity_ratios))]

    # Create bar colors based on threshold
    colors = ['#1f77b4' if ratio >= threshold else '#d3d3d3' for ratio in selectivity_ratios]

    # Plot bars
    ax.bar(x, selectivity_ratios, color=colors)

    # Add threshold line
    ax.axhline(y=threshold, color='r', linestyle='--', label=f'Threshold ({threshold})')

    # Add labels and title
    ax.set_xlabel('Variables')
    ax.set_ylabel('Selectivity Ratio')
    ax.set_title('Selectivity Ratio (SR) Results')

    # Add x-ticks with variable names if there are not too many
    if len(variable_names) <= 30:
        ax.set_xticks(x)
        ax.set_xticklabels(variable_names, rotation=90)
    else:
        # Show only some variable names
        step = max(1, len(variable_names) // 20)
        ax.set_xticks(x[::step])
        ax.set_xticklabels([variable_names[i] for i in range(0, len(variable_names), step)], rotation=90)

    # Add legend
    ax.legend()

    # Adjust layout
    plt.tight_layout()

    return fig

def plot_selected_variables_heatmap(X, selected_vars, variable_names=None, figsize=(12, 8)):
    """
    Plot heatmap of selected variables.

    Parameters:
    -----------
    X : array-like
        Input data
    selected_vars : array-like
        Boolean array indicating selected variables
    variable_names : list, optional
        Names of variables
    figsize : tuple
        Figure size

    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    """
    # Get selected data and variable names
    X_selected = X[:, selected_vars]

    if variable_names is None:
        variable_names = [f'Var {i+1}' for i in range(len(selected_vars))]

    selected_names = [variable_names[i] for i in range(len(selected_vars)) if selected_vars[i]]

    # Create figure
    fig, ax = plt.subplots(figsize=figsize)

    # Create heatmap
    im = ax.imshow(X_selected.T, aspect='auto', cmap='viridis')

    # Add colorbar
    cbar = ax.figure.colorbar(im, ax=ax)
    cbar.ax.set_ylabel('Value', rotation=-90, va="bottom")

    # Add labels and title
    ax.set_xlabel('Samples')
    ax.set_ylabel('Selected Variables')
    ax.set_title('Heatmap of Selected Variables')

    # Add y-ticks with variable names if there are not too many
    if len(selected_names) <= 30:
        ax.set_yticks(np.arange(len(selected_names)))
        ax.set_yticklabels(selected_names)
    else:
        # Show only some variable names
        step = max(1, len(selected_names) // 20)
        ax.set_yticks(np.arange(0, len(selected_names), step))
        ax.set_yticklabels([selected_names[i] for i in range(0, len(selected_names), step)])

    # Adjust layout
    plt.tight_layout()

    return fig

"""
True NIPALS (Nonlinear Iterative Partial Least Squares) Algorithm Implementation
Based on Johnson & Johnson's open_nipals and chemometric literature.

References:
- <PERSON><PERSON><PERSON>, P<PERSON>; <PERSON>, B. R. Partial Least-Squares Regression: A Tutorial. 
  Analytica Chimica Acta 1986, 185, 1–17.
- <PERSON>, <PERSON>, <PERSON>, and <PERSON>. 
  Fault detection and diagnosis in industrial systems. Springer, 2000.
"""

import numpy as np
import warnings
from sklearn.base import BaseEstimator, RegressorMixin, TransformerMixin
from sklearn.utils.validation import check_X_y, check_array, check_is_fitted
from sklearn.metrics import r2_score, mean_squared_error
from typing import Optional, Tuple, Dict, Any


class NIPALSRegression(BaseEstimator, RegressorMixin, TransformerMixin):
    """
    True NIPALS (Nonlinear Iterative Partial Least Squares) implementation.
    
    This implementation provides the authentic NIPALS algorithm as described
    in chemometric literature, distinct from SIMPLS used in scikit-learn.
    
    Parameters
    ----------
    n_components : int, default=2
        Number of PLS components to extract
    max_iter : int, default=500
        Maximum number of iterations for convergence
    tol : float, default=1e-6
        Tolerance for convergence criteria
    scale : bool, default=True
        Whether to scale X and Y to unit variance
    copy : bool, default=True
        Whether to copy X and Y or modify in place
    mode : str, default='PLS1'
        PLS mode: 'PLS1' for single Y, 'PLS2' for multiple Y
    """
    
    def __init__(self, n_components: int = 2, max_iter: int = 500, 
                 tol: float = 1e-6, scale: bool = True, copy: bool = True,
                 mode: str = 'PLS1'):
        self.n_components = n_components
        self.max_iter = max_iter
        self.tol = tol
        self.scale = scale
        self.copy = copy
        self.mode = mode
        
        # Initialize attributes
        self.x_weights_ = None
        self.y_weights_ = None
        self.x_loadings_ = None
        self.y_loadings_ = None
        self.x_scores_ = None
        self.y_scores_ = None
        self.x_rotations_ = None
        self.y_rotations_ = None
        self.coef_ = None
        self.intercept_ = None
        self.n_iter_ = []
        
        # Scaling parameters
        self._x_mean = None
        self._y_mean = None
        self._x_std = None
        self._y_std = None
    
    def _center_scale_xy(self, X: np.ndarray, Y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Center and optionally scale X and Y matrices."""
        # Center
        self._x_mean = np.mean(X, axis=0)
        self._y_mean = np.mean(Y, axis=0)
        X_centered = X - self._x_mean
        Y_centered = Y - self._y_mean
        
        # Scale if requested
        if self.scale:
            self._x_std = np.std(X_centered, axis=0, ddof=1)
            self._y_std = np.std(Y_centered, axis=0, ddof=1)
            
            # Avoid division by zero
            self._x_std[self._x_std == 0.0] = 1.0
            self._y_std[self._y_std == 0.0] = 1.0
            
            X_centered /= self._x_std
            Y_centered /= self._y_std
        else:
            self._x_std = np.ones(X.shape[1])
            self._y_std = np.ones(Y.shape[1])
        
        return X_centered, Y_centered
    
    def _nipals_iteration(self, X: np.ndarray, Y: np.ndarray) -> Tuple[np.ndarray, np.ndarray, 
                                                                      np.ndarray, np.ndarray, 
                                                                      np.ndarray, int]:
        """
        Perform one NIPALS iteration to extract one component.
        
        Returns
        -------
        t : X scores
        u : Y scores  
        p : X loadings
        q : Y loadings
        w : X weights
        n_iter : number of iterations for convergence
        """
        n_samples, n_features = X.shape
        n_targets = Y.shape[1]
        
        # Initialize Y scores with column of Y having maximum variance
        if self.mode == 'PLS1' or n_targets == 1:
            u = Y[:, [0]].copy()
        else:  # PLS2
            y_var = np.var(Y, axis=0)
            max_var_idx = np.argmax(y_var)
            u = Y[:, [max_var_idx]].copy()
        
        # Initialize iteration
        t_old = np.zeros((n_samples, 1))
        
        for iteration in range(self.max_iter):
            # Step 1: Calculate X weights
            w = X.T @ u
            w = w / np.linalg.norm(w)  # Normalize weights
            
            # Step 2: Calculate X scores
            t = X @ w
            
            # Step 3: Calculate Y weights
            if self.mode == 'PLS1' or n_targets == 1:
                q = Y.T @ t / (t.T @ t)
            else:  # PLS2
                q = Y.T @ t
                q = q / np.linalg.norm(q)  # Normalize for PLS2
            
            # Step 4: Calculate Y scores
            u = Y @ q
            if self.mode == 'PLS2' and n_targets > 1:
                u = u / (q.T @ q)  # Normalize for PLS2
            
            # Check convergence
            diff = np.linalg.norm(t - t_old)
            if diff < self.tol:
                break
            
            t_old = t.copy()
        
        # Calculate X loadings
        p = X.T @ t / (t.T @ t)
        
        return t, u, p, q, w, iteration + 1
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'NIPALSRegression':
        """
        Fit the NIPALS model.
        
        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Training data
        y : array-like of shape (n_samples,) or (n_samples, n_targets)
            Target values
            
        Returns
        -------
        self : object
        """
        # Validate input
        X, y = check_X_y(X, y, multi_output=True, y_numeric=True, copy=self.copy)
        
        if y.ndim == 1:
            y = y.reshape(-1, 1)
        
        n_samples, n_features = X.shape
        n_targets = y.shape[1]
        
        # Validate n_components
        max_components = min(n_samples - 1, n_features)
        if self.n_components > max_components:
            warnings.warn(f"n_components ({self.n_components}) exceeds maximum "
                         f"({max_components}). Setting to {max_components}.")
            self.n_components = max_components
        
        # Center and scale data
        X_work, Y_work = self._center_scale_xy(X, y)
        
        # Initialize matrices
        self.x_weights_ = np.zeros((n_features, self.n_components))
        self.y_weights_ = np.zeros((n_targets, self.n_components))
        self.x_loadings_ = np.zeros((n_features, self.n_components))
        self.y_loadings_ = np.zeros((n_targets, self.n_components))
        self.x_scores_ = np.zeros((n_samples, self.n_components))
        self.y_scores_ = np.zeros((n_samples, self.n_components))
        self.n_iter_ = []
        
        # Extract components iteratively
        for k in range(self.n_components):
            # Perform NIPALS iteration
            t, u, p, q, w, n_iter = self._nipals_iteration(X_work, Y_work)
            
            # Store results
            self.x_scores_[:, [k]] = t
            self.y_scores_[:, [k]] = u
            self.x_loadings_[:, [k]] = p
            self.y_loadings_[:, [k]] = q
            self.x_weights_[:, [k]] = w
            self.n_iter_.append(n_iter)
            
            # Deflate X and Y matrices
            X_work = X_work - t @ p.T
            
            if self.mode == 'PLS1':
                # For PLS1, deflate Y using X scores
                Y_work = Y_work - t @ q.T
            else:
                # For PLS2, deflate Y using Y scores
                Y_work = Y_work - u @ q.T
        
        # Calculate rotation matrices
        self.x_rotations_ = self.x_weights_ @ np.linalg.pinv(
            self.x_loadings_.T @ self.x_weights_
        )
        
        self.y_rotations_ = self.y_weights_ @ np.linalg.pinv(
            self.y_loadings_.T @ self.y_weights_
        )
        
        # Calculate regression coefficients
        self.coef_ = self.x_rotations_ @ self.y_loadings_.T
        self.coef_ = (self.coef_ * self._y_std).T / self._x_std
        self.intercept_ = self._y_mean
        
        return self
    
    def transform(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> np.ndarray:
        """Transform X to scores space."""
        check_is_fitted(self)
        X = check_array(X, copy=self.copy)
        
        # Center and scale
        X_scaled = (X - self._x_mean) / self._x_std
        
        # Transform to scores
        x_scores = X_scaled @ self.x_rotations_
        
        if y is not None:
            y = check_array(y, ensure_2d=False, copy=self.copy)
            if y.ndim == 1:
                y = y.reshape(-1, 1)
            y_scaled = (y - self._y_mean) / self._y_std
            y_scores = y_scaled @ self.y_rotations_
            return x_scores, y_scores
        
        return x_scores
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict using the NIPALS model."""
        check_is_fitted(self)
        X = check_array(X, copy=self.copy)
        
        # Center but don't scale (coefficients already account for scaling)
        X_centered = X - self._x_mean
        y_pred = X_centered @ self.coef_.T + self.intercept_
        
        return y_pred.ravel() if y_pred.shape[1] == 1 else y_pred
    
    def fit_transform(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """Fit model and return X scores."""
        return self.fit(X, y).transform(X)
    
    def score(self, X: np.ndarray, y: np.ndarray) -> float:
        """Return R² score."""
        y_pred = self.predict(X)
        return r2_score(y, y_pred)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information for display."""
        check_is_fitted(self)
        
        return {
            'algorithm': 'True NIPALS',
            'n_components': self.n_components,
            'mode': self.mode,
            'max_iter': self.max_iter,
            'tol': self.tol,
            'n_iter_per_component': self.n_iter_,
            'total_iterations': sum(self.n_iter_),
            'converged': all(n < self.max_iter for n in self.n_iter_)
        }

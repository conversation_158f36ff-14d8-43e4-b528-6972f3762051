"""
This module contains information about different PLS algorithms, variable selection methods,
data preprocessing methods, and data compression techniques to guide users.
"""

# PLS Algorithm Information
PLS_ALGORITHM_INFO = {
    "Standard PLS": """
    **Standard PLS (Partial Least Squares)**

    The standard implementation of PLS regression that finds the directions in the predictor space
    that explain the maximum variance in both the predictor and response variables.

    **Key characteristics:**
    - Implemented in scikit-learn
    - Robust and well-established algorithm
    - Good for handling multicollinearity
    - Works well with high-dimensional data
    """,

    "NIPALS": """
    **NIPALS (Nonlinear Iterative Partial Least Squares)**

    The original algorithm for PLS that uses an iterative approach to extract latent variables.

    **Key characteristics:**
    - The classic implementation of PLS
    - Handles missing data better than other algorithms
    - Slightly slower than other methods
    - More numerically stable for certain datasets
    """,

    "SIMPLS": """
    **SIMPLS (Statistically Inspired Modification of PLS)**

    A faster alternative to NIPALS that directly computes the PLS factors as linear combinations
    of the original variables.

    **Key characteristics:**
    - Faster than NIPALS
    - Provides the same results as NIPALS for single-response models
    - May differ slightly from NIPALS for multi-response models
    - Often more numerically stable
    """,

    "Kernel PLS": """
    **Kernel PLS**

    A nonlinear extension of PLS that uses kernel methods to capture nonlinear relationships
    between predictors and responses.

    **Key characteristics:**
    - Handles nonlinear relationships
    - Uses kernel functions (RBF, polynomial, etc.)
    - More computationally intensive
    - Good for complex, nonlinear data

    **Parameters:**
    - Kernel type: The type of kernel function to use (RBF, linear, polynomial, etc.)
    - Gamma: Kernel coefficient for RBF, polynomial, and sigmoid kernels
    - Degree: Degree of the polynomial kernel (only for polynomial kernel)
    """,

    "OPLS": """
    **OPLS (Orthogonal Projections to Latent Structures)**

    A variant of PLS that separates predictive and non-predictive (orthogonal) variation in the data.

    **Key characteristics:**
    - Improves interpretability by removing orthogonal variation
    - Works best with a single response variable
    - Helps identify and remove systematic variation unrelated to the response
    - Useful for spectroscopic data with baseline variations
    """
}

# Variable Selection Method Information
VARIABLE_SELECTION_INFO = {
    "VIP filtering": """
    **VIP (Variable Importance in Projection) Filtering**

    Selects variables based on their importance in the PLS model, as measured by the VIP score.

    **Key characteristics:**
    - VIP scores measure the contribution of each variable to the model
    - Variables with VIP > threshold (typically 1.0) are considered important
    - Simple and widely used method
    - Directly related to the PLS model structure

    **Parameters:**
    - VIP threshold: Variables with VIP scores above this threshold are selected
    """,

    "Genetic Algorithm": """
    **Genetic Algorithm (GA)**

    Uses evolutionary algorithms to find the optimal subset of variables.

    **Key characteristics:**
    - Mimics natural selection to evolve variable subsets
    - Can find global optima in complex search spaces
    - Computationally intensive but effective
    - Stochastic nature may give different results on different runs

    **Parameters:**
    - Population size: Number of variable subsets in each generation
    - Number of generations: How many iterations to evolve the population
    - Crossover probability: Likelihood of combining two parent solutions
    - Mutation probability: Likelihood of random changes in solutions
    """,

    "Interval PLS (iPLS)": """
    **Interval PLS (iPLS)**

    Divides the variables into intervals and selects the interval that gives the best model performance.

    **Key characteristics:**
    - Particularly useful for spectroscopic data
    - Helps identify important spectral regions
    - Simple and interpretable
    - Can miss important variables if they span multiple intervals

    **Parameters:**
    - Number of intervals: How many segments to divide the variables into
    """,

    "Backward Interval PLS (BiPLS)": """
    **Backward Interval PLS (BiPLS)**

    Starts with all intervals and iteratively removes the least important ones.

    **Key characteristics:**
    - Extension of iPLS with backward elimination
    - Often performs better than standard iPLS
    - More computationally intensive than iPLS
    - Good for identifying multiple important regions

    **Parameters:**
    - Number of intervals: How many segments to divide the variables into
    """,

    "Moving Window PLS (MWPLS)": """
    **Moving Window PLS (MWPLS)**

    Uses a sliding window to identify important variable regions.

    **Key characteristics:**
    - Good for finding continuous regions of important variables
    - Particularly useful for spectroscopic data
    - More detailed than interval methods
    - Can be computationally intensive

    **Parameters:**
    - Window size: Number of variables in each window
    - Step size: How many variables to move the window in each step
    """,

    "Competitive Adaptive Reweighted Sampling (CARS)": """
    **Competitive Adaptive Reweighted Sampling (CARS)**

    Iteratively selects variables by adaptive reweighting based on regression coefficients.

    **Key characteristics:**
    - Combines exponential decay function with adaptive reweighting
    - Effective for high-dimensional data
    - Robust to noise
    - Modern method with good performance

    **Parameters:**
    - Number of sampling runs: How many iterations to perform
    - Initial fraction: Starting percentage of variables to keep
    - Final fraction: Ending percentage of variables to keep
    """,

    "Uninformative Variable Elimination (UVE)": """
    **Uninformative Variable Elimination (UVE)**

    Eliminates variables that are not significantly more informative than random noise.

    **Key characteristics:**
    - Adds artificial noise variables to the data
    - Compares real variables to noise variables
    - Eliminates variables that are not significantly better than noise
    - Good for removing truly uninformative variables

    **Parameters:**
    - Number of iterations: How many Monte Carlo iterations to perform
    - Confidence level: Statistical confidence for variable selection
    """,

    "Selectivity Ratio (SR)": """
    **Selectivity Ratio (SR)**

    Selects variables based on the ratio of explained to residual variance.

    **Key characteristics:**
    - Based on target-projected PLS models
    - Measures the ratio of explained to unexplained variance for each variable
    - Good for identifying variables with high signal-to-noise ratio
    - Particularly useful for spectroscopic data

    **Parameters:**
    - Selectivity ratio threshold: Variables with SR above this threshold are selected
    """
}

# Preprocessing Method Information
PREPROCESSING_INFO = {
    "Mean-centering": """
    **Mean-centering**

    Subtracts the mean of each variable from all observations, centering the data around zero.

    **Key characteristics:**
    - Removes the offset from the data
    - Makes variables comparable regardless of their original scale
    - Simplest form of preprocessing
    - Preserves the variance structure of the data
    - Often used as a baseline preprocessing method
    """,

    "Autoscaling": """
    **Autoscaling (Standardization)**

    Subtracts the mean and divides by the standard deviation of each variable, resulting in variables with zero mean and unit variance.

    **Key characteristics:**
    - Makes all variables equally important regardless of their original scale
    - Useful when variables have different units or scales
    - Can amplify noise in variables with low variance
    - Standard approach for many machine learning methods
    """,

    "SNV": """
    **Standard Normal Variate (SNV)**

    Normalizes each observation (spectrum) by subtracting its mean and dividing by its standard deviation.

    **Key characteristics:**
    - Removes baseline offset and scaling effects
    - Applied row-wise (to each spectrum) rather than column-wise
    - Particularly useful for spectroscopic data
    - Helps correct for scatter effects in spectroscopy
    - Preserves the shape of spectral features
    """,

    "Savitzky-Golay": """
    **Savitzky-Golay Filtering**

    Applies a polynomial smoothing and differentiation filter to the data.

    **Key characteristics:**
    - Smooths data while preserving peak shapes
    - Can calculate derivatives to enhance spectral features
    - Reduces noise while maintaining signal integrity
    - Particularly useful for spectroscopic data
    - First derivative helps remove baseline offsets
    - Second derivative helps resolve overlapping peaks
    """,

    "SNV + Savitzky-Golay": """
    **SNV + Savitzky-Golay**

    Combines SNV normalization with Savitzky-Golay filtering for enhanced preprocessing.

    **Key characteristics:**
    - Removes baseline and scaling effects (SNV)
    - Enhances spectral features (Savitzky-Golay)
    - Powerful combination for spectroscopic data
    - Helps resolve overlapping peaks while normalizing spectra
    - Often provides better results than either method alone
    """
}

# Data Compression Method Information
DATA_COMPRESSION_INFO = {
    "None": """
    **No Data Compression**

    Uses the original data without any compression.
    """,

    "PCA": """
    **Principal Component Analysis (PCA)**

    Reduces dimensionality by finding orthogonal directions of maximum variance in the data.

    **Key characteristics:**
    - Unsupervised method (doesn't use response variables)
    - Captures the most variance with the fewest components
    - Fast and widely used
    - May lose information relevant to the prediction task

    **Parameters:**
    - Number of components: How many principal components to retain
    """,

    "ICA": """
    **Independent Component Analysis (ICA)**

    Separates multivariate signals into additive, statistically independent components.

    **Key characteristics:**
    - Finds statistically independent sources
    - Useful for separating mixed signals
    - Can reveal hidden factors in the data
    - More computationally intensive than PCA

    **Parameters:**
    - Number of components: How many independent components to extract
    """,

    "Wavelet Transform": """
    **Wavelet Transform**

    Decomposes signals into wavelets at different scales and positions.

    **Key characteristics:**
    - Good for analyzing signals with discontinuities
    - Provides time-frequency localization
    - Effective for denoising
    - Particularly useful for spectroscopic data

    **Parameters:**
    - Wavelet type: The wavelet function to use (e.g., db4, sym8, haar)
    - Decomposition level: How many levels of decomposition to perform
    - Threshold: Threshold for coefficient thresholding (denoising)
    """,

    "Fourier Transform": """
    **Fourier Transform**

    Decomposes signals into their frequency components.

    **Key characteristics:**
    - Converts signals from time/space domain to frequency domain
    - Good for periodic signals
    - Fast implementation available (FFT)
    - Loses time/space localization

    **Parameters:**
    - Number of components: How many frequency components to retain
    """,

    "DCT": """
    **Discrete Cosine Transform (DCT)**

    Expresses signals as a sum of cosine functions at different frequencies.

    **Key characteristics:**
    - Similar to Fourier Transform but uses only cosines
    - Better energy compaction than Fourier Transform
    - Used in JPEG image compression
    - Good for smooth signals

    **Parameters:**
    - Number of components: How many DCT coefficients to retain
    """
}
